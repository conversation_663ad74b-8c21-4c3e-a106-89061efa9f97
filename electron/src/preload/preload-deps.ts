/**
 * Preload脚本专用依赖 - 单文件集中管理
 * 避免复杂的模块解析问题，使用相对路径引用
 */

// ==================== 常量定义 ====================
// Preload环境需要独立定义，避免模块依赖问题

export const ELECTRON_WEBUI_PROTOCOL = 'wexond';
export const ELECTRON_ERROR_PROTOCOL = 'wexond-error';
export const ELECTRON_NETWORK_ERROR_HOST = 'network-error';

export const ELECTRON_WEBUI_BASE_URL = process.env.NODE_ENV === 'development'
  ? 'http://localhost:4444/'
  : `${ELECTRON_WEBUI_PROTOCOL}://`;

export const ELECTRON_WEBUI_URL_SUFFIX = ELECTRON_WEBUI_BASE_URL.startsWith('http')
  ? '.html'
  : '';

// ==================== 类型定义 ====================

export interface IFormFillData {
  _id?: string;
  url: string;
  fields: {
    username: string;
    password: string;
  };
}

// ==================== 工具函数 ====================

/**
 * 生成随机ID
 */
export const makeId = (length: number): string => {
  let result = '';
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
};

/**
 * 获取主题配置
 */
export const getTheme = (name: string) => {
  if (name === 'wexond-dark') {
    return { 'dialog.lightForeground': true };
  }
  return { 'dialog.lightForeground': false };
};

/**
 * 表单字段过滤器
 */
export const formFieldFilters = {
  type: /text|email|password/i,
  name: /login|username|email|password|name|fname|mname|lname|phone|mobile|address|city|country/i,
  menu: /login|username|email|password|name|mname|lname|phone|mobile|address/i,
};
