/**
 * 对话框预加载脚本
 * 确保对话框页面能够访问Electron API，即使在开发环境中通过vite服务器加载
 */

import { ipcRenderer } from 'electron';

// 内联getTheme函数，避免复杂的路径解析
const getTheme = (name: string) => {
  // 简化的主题获取逻辑
  if (name === 'wexond-dark') {
    return { 'dialog.lightForeground': true };
  }
  return { 'dialog.lightForeground': false };
};

// 获取窗口ID
const windowId: number = ipcRenderer.sendSync('get-window-id');

console.log('[DialogPreload] Dialog preload script loaded, windowId:', windowId);

// 在window对象上暴露必要的API
(window as any).require = (id: string) => {
  console.log('[DialogPreload] require called with:', id);
  if (id === 'electron') {
    return { ipcRenderer };
  }
  if (id === '@electron/remote') {
    try {
      return require('@electron/remote');
    } catch (error) {
      console.warn('[DialogPreload] @electron/remote not available:', error);
      return null;
    }
  }
  return undefined;
};

// 暴露ipcRenderer到全局
(window as any).ipcRenderer = ipcRenderer;

// 暴露设置
const settings = ipcRenderer.sendSync('get-settings-sync');
(window as any).settings = settings;

// 暴露主题
(window as any).getTheme = getTheme;

// 暴露窗口ID
(window as any).windowId = windowId;

console.log('[DialogPreload] APIs exposed to window object');

// 监听页面加载完成
window.addEventListener('DOMContentLoaded', () => {
  console.log('[DialogPreload] DOM content loaded');
  
  // 通知主进程对话框已准备就绪
  ipcRenderer.send('dialog-ready', windowId);
});
