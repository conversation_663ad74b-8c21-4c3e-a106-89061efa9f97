import { ipcRenderer, contextBridge } from 'electron';
import { toJS } from 'mobx';

// 全局IPC调试：拦截所有IPC发送，检查数据序列化
const originalSend = ipcRenderer.send;
ipcRenderer.send = function(channel: string, ...args: any[]) {
  console.log(`[IPC-DEBUG-APP] Sending to channel: ${channel}`, args);

  // 检查每个参数是否可序列化
  args.forEach((arg, index) => {
    try {
      JSON.stringify(arg);
    } catch (error) {
      console.error(`[IPC-DEBUG-APP] Non-serializable argument at index ${index} for channel ${channel}:`, error);
      console.error(`[IPC-DEBUG-APP] Problematic data:`, arg);
      console.error(`[IPC-DEBUG-APP] Stack trace:`, new Error().stack);
    }
  });

  return originalSend.call(this, channel, ...args);
};

// 获取窗口ID
const windowId: number = ipcRenderer.sendSync('get-window-id');

console.log('[AppPreload] App preload script loaded, window ID:', windowId);

// 暴露IPC API到渲染进程
const electronAPI = {
  ipcRenderer: {
    send: (channel: string, ...args: any[]) => {
      console.log('[AppPreload] Sending IPC event:', channel, args);
      // 序列化MobX对象，确保IPC通信安全
      const serializedArgs = args.map(arg => toJS(arg));
      ipcRenderer.send(channel, ...serializedArgs);
    },
    invoke: (channel: string, ...args: any[]) => {
      console.log('[AppPreload] Invoking IPC:', channel, args);
      // 序列化MobX对象，确保IPC通信安全
      const serializedArgs = args.map(arg => toJS(arg));
      return ipcRenderer.invoke(channel, ...serializedArgs);
    },
    on: (channel: string, listener: any) => {
      console.log('[AppPreload] Registering IPC listener:', channel);
      ipcRenderer.on(channel, listener);
    },
    removeAllListeners: (channel: string) => {
      ipcRenderer.removeAllListeners(channel);
    }
  },
  windowId
};

// 使用contextBridge安全地暴露API
try {
  contextBridge.exposeInMainWorld('electronAPI', electronAPI);
  console.log('[AppPreload] Context bridge API exposed successfully');
} catch (error) {
  console.error('[AppPreload] Failed to expose context bridge API:', error);
  // 如果contextBridge失败，直接挂载到window（不安全但可用）
  (window as any).electronAPI = electronAPI;
  console.log('[AppPreload] Fallback: API attached to window');
}
