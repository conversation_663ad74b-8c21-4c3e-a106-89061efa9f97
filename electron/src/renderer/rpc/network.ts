import { RendererToMainChannel } from '@wexond/rpc-electron';

export interface ResponseDetails {
  statusCode: number;
  data: string;
}

export interface NetworkService {
  request(url: string): Promise<ResponseDetails>;
}

let _networkMainChannel: RendererToMainChannel<NetworkService>;

export const getNetworkMainChannel = () => {
  if (!_networkMainChannel) {
    _networkMainChannel = new RendererToMainChannel<NetworkService>(
      'NetworkService',
    );
  }
  return _networkMainChannel;
};

// 为了向后兼容
export const networkMainChannel = {
  getReceiver: () => getNetworkMainChannel().getReceiver(),
};
