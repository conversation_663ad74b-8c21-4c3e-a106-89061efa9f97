export const DIRECTORIES = ['adblock', 'extensions', 'storage'];

export const ELECTRON_WEBUI_PROTOCOL = 'wexond';

export const ELECTRON_ERROR_PROTOCOL = 'wexond-error';

export const ELECTRON_NETWORK_ERROR_HOST = 'network-error';

export const ELECTRON_WEBUI_BASE_URL =
  process.env.NODE_ENV === 'development'
    ? 'http://localhost:4444/'
    : `${ELECTRON_WEBUI_PROTOCOL}://`;

export const ELECTRON_WEBUI_URL_SUFFIX = ELECTRON_WEBUI_BASE_URL.startsWith('http')
  ? '.html'
  : '';
