import { protocol } from 'electron';
import { join } from 'path';
import { parse } from 'url';
import { ELECTRON_ERROR_PROTOCOL, ELECTRON_WEBUI_PROTOCOL } from '@electron/renderer/constants/files';

protocol.registerSchemesAsPrivileged([
  {
    scheme: 'wexond',
    privileges: {
      bypassCSP: true,
      secure: true,
      standard: true,
      supportFetchAPI: true,
      allowServiceWorkers: true,
      corsEnabled: true,
    },
  },
  {
    scheme: 'chrome-extension',
    privileges: {
      bypassCSP: true,
      secure: true,
      standard: true,
      supportFetchAPI: true,
      allowServiceWorkers: true,
      corsEnabled: true,
      stream: true,
      codeCache: true
    },
  },
]);

export const registerProtocol = (session: Electron.Session) => {
  session.protocol.registerFileProtocol(
    ELECTRON_ERROR_PROTOCOL,
    (request, callback: any) => {
      const parsed = parse(request.url);

      if (parsed.hostname === 'network-error') {
        return callback({
          path: join(__dirname, '../pages/', `network-error.html`),
        });
      }
    },
  );

  if (process.env.NODE_ENV !== 'development') {
    session.protocol.registerFileProtocol(
      ELECTRON_WEBUI_PROTOCOL,
      (request, callback: any) => {
        const parsed = parse(request.url);

        if (parsed.path === '/') {
          return callback({
            path: join(__dirname, `${parsed.hostname}.html`),
          });
        }
        if (parsed.hostname === 'web.koodoreader.com') {
          return callback({
            path: join(__dirname, '../koodo-reader', parsed.path === '/' ? 'index.html' : parsed.path),
          });
        }

        callback({ path: join(__dirname, parsed.path) });
      },
    );
  }
};
