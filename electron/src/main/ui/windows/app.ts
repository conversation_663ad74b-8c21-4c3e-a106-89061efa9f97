import {BrowserWindow, app, dialog, ipcMain} from 'electron';
import {writeFileSync, promises} from 'fs';
import {resolve, join} from 'path';

import {getPath} from '@electron/renderer/utils/paths';
import {runMessagingService} from '@electron/main/services';
import {Application} from '@electron/main/core/application';
import {isNightly} from '@electron/main/core';
import {ViewManager} from '@electron/main/services/view-manager';
import {LightweightAIToolbar} from '@electron/main/services/lightweight-ai-toolbar';
import {PopupView} from "../dialogs/popup";

export class AppWindow {
  public win: BrowserWindow;

  public viewManager: ViewManager;
  public lightweightAIToolbar: LightweightAIToolbar;

  public incognito: boolean;
  public popup: PopupView;

  public constructor(incognito: boolean) {
    this.win = new BrowserWindow({
      frame: false,
      minWidth: 800,
      minHeight: 450,
      width: 900,
      height: 600,
      titleBarStyle: 'hiddenInset',
      backgroundColor: '#ffffff',
      webPreferences: {
        plugins: true,
        // TODO: enable sandbox, contextIsolation and disable nodeIntegration to improve security
        nodeIntegration: true,
        allowRunningInsecureContent: true,
        webSecurity: false,
        nodeIntegrationInWorker: true,
        nodeIntegrationInSubFrames: true,
        enableWebSQL: true,
        contextIsolation: false,
        javascript: true,
        //sandbox: false,
        // TODO: get rid of the remote module in renderers
        enableRemoteModule: true,
        worldSafeExecuteJavaScript: true,
      },
      icon: resolve(
        app.getAppPath(),
        `static/${isNightly ? 'nightly-icons' : 'icons'}/icon.png`,
      ),
      show: false,
    });
    require("@electron/remote/main").enable(this.win.webContents);

    this.incognito = incognito;

    this.viewManager = new ViewManager(this, incognito);
    this.lightweightAIToolbar = new LightweightAIToolbar(this);

    console.log('[AppWindow] Starting messaging service for window ID:', this.id);
    runMessagingService(this);
    console.log('[AppWindow] Messaging service started for window ID:', this.id);

    if (process.env.ENABLE_EXTENSIONS) {
      ipcMain.on(`show-extension-popup-${this.id}`, (e, left, top, url, inspect, extensionId) => {
        if (this.popup) {
          const toggleExtension = !this.popup.isDestroyed() && this.popup.extensionId === extensionId
          this.popup.destroy()
          this.popup = undefined
          if (toggleExtension) {
            return
          }
        }
        this.popup = new PopupView({
          extensionId,
          session: incognito ? Application.instance.sessions.viewIncognito : Application.instance.sessions.view,
          parent: this.win,
          url: url,
          anchorRect: {
            x: left,
            y: top,
            height: 0,
            width: 0
          }
        })
      });
    }

    const windowDataPath = getPath('window-data.json');

    let windowState: any = {};

    (async () => {
      try {
        // Read the last window state from file.
        windowState = JSON.parse(
          await promises.readFile(windowDataPath, 'utf8'),
        );
      } catch (e) {
        await promises.writeFile(windowDataPath, JSON.stringify({}));
      }

      // Merge bounds from the last window state to the current window options.
      if (windowState) {
        this.win.setBounds({...windowState.bounds});
      }

      if (windowState) {
        if (windowState.maximized) {
          this.win.maximize();
        }
        if (windowState.fullscreen) {
          this.win.setFullScreen(true);
        }
      }
      this.initWin(windowState, windowDataPath, incognito);
    })();
  }

  public initWin = (windowState: any, windowDataPath: string, incognito: boolean) => {
    this.win.show();
    // Update window bounds on resize and on move when window is not maximized.
    this.win.on('resize', () => {
      if (!this.win.isMaximized()) {
        windowState.bounds = this.win.getBounds();
      }
    });

    this.win.on('move', () => {
      if (!this.win.isMaximized()) {
        windowState.bounds = this.win.getBounds();
      }
    });

    const resize = () => {
      setTimeout(() => {
        // 更新轻量级AI工具栏位置
        this.lightweightAIToolbar?.updatePosition();

        if (process.platform === 'linux') {
          this.viewManager.select(this.viewManager.selectedId, false, true);
        } else {
          this.viewManager.fixBounds();
        }
      });

      setTimeout(() => {
        this.webContents.send('tabs-resize');
      }, 500);

      this.webContents.send('tabs-resize');
    };

    this.win.on('maximize', resize);
    this.win.on('restore', resize);
    this.win.on('unmaximize', resize);

    this.win.on('close', async (event: Electron.Event) => {
      try {
        const warnOnQuit = await Application.instance.storage.getConfig('warnOnQuit');

        if (warnOnQuit && this.viewManager.views.size > 1) {
          const answer = dialog.showMessageBoxSync(null, {
            type: 'question',
            title: `退出 ${app.name}?`,
            message: `退出 ${app.name}?`,
            detail: `有 ${this.viewManager.views.size} 打开的标签页.`,
            buttons: ['关闭', '取消'],
          });

          if (answer === 1) {
            event.preventDefault();
            return;
          }
        }
      } catch (error) {
        console.error('[AppWindow] 获取warnOnQuit配置失败:', error);
      }

      // Save current window state to a file.
      windowState.maximized = this.win.isMaximized();
      windowState.fullscreen = this.win.isFullScreen();
      writeFileSync(windowDataPath, JSON.stringify(windowState));

      this.win.setBrowserView(null);

      this.viewManager.clear();

      if (Application.instance.windows.list.length === 1) {
        Application.instance.dialogs.destroy();
      }

      if (
        incognito &&
        Application.instance.windows.list.filter((x) => x.incognito).length ===
        1
      ) {
        Application.instance.sessions.clearCache('incognito');
        Application.instance.sessions.unloadIncognitoExtensions();
      }

      Application.instance.windows.list = Application.instance.windows.list.filter(
        (x) => x.win.id !== this.win.id,
      );
    });

    // this.webContents.openDevTools({ mode: 'detach' });

    if (process.env.NODE_ENV === 'development') {
      this.webContents.openDevTools({mode: 'detach'});
      this.win.loadURL('http://localhost:4444/app.html');
    } else {
      //this.webContents.openDevTools({mode: 'detach'});
      this.win.loadURL(join('file://', app.getAppPath(), 'build/app.html'));
    }
    this.win.on('ready-to-show', async () => {
      // 等初始化完成后再显示
      this.win.show();

      // 创建轻量级AI工具栏
      try {
        await this.lightweightAIToolbar.create();
        console.log('[AppWindow] Lightweight AI toolbar created successfully');
      } catch (error) {
        console.error('[AppWindow] Failed to create lightweight AI toolbar:', error);
      }
    });


    this.win.on('enter-full-screen', () => {
      this.send('fullscreen', true);
      this.lightweightAIToolbar?.handleFullScreenChange(true);
      this.viewManager.fixBounds();
    });

    this.win.on('leave-full-screen', () => {
      this.send('fullscreen', false);
      this.lightweightAIToolbar?.handleFullScreenChange(false);
      this.viewManager.fixBounds();
    });

    this.win.on('enter-html-full-screen', () => {
      this.viewManager.fullscreen = true;
      this.send('html-fullscreen', true);
    });

    this.win.on('leave-html-full-screen', () => {
      this.viewManager.fullscreen = false;
      this.send('html-fullscreen', false);
    });

    this.win.on('scroll-touch-begin', () => {
      this.send('scroll-touch-begin');
    });

    this.win.on('scroll-touch-end', () => {
      this.viewManager.selected.send('scroll-touch-end');
      this.send('scroll-touch-end');
    });

    this.win.on('focus', () => {
      Application.instance.windows.current = this;
    });
  }

  public get id() {
    return this.win.id;
  }

  public get webContents() {
    return this.win.webContents;
  }

  public fixDragging() {
    const bounds = this.win.getBounds();
    this.win.setBounds({
      height: bounds.height + 1,
    });
    this.win.setBounds(bounds);
  }

  public send(channel: string, ...args: any[]) {
    // ✅ 重构: 添加详细调试信息，定位序列化错误源 (修复序列化问题)
    console.log(`[AppWindow] Sending to channel: ${channel}, args count: ${args.length}`);

    // 序列化所有参数，确保IPC通信安全
    const serializedArgs = args.map((arg, index) => {
      if (arg === null || arg === undefined) return arg;
      if (typeof arg === 'function') return '[Function]';
      if (typeof arg === 'symbol') return '[Symbol]';
      if (arg instanceof Error) return { message: arg.message, stack: arg.stack };

      // 对于对象，进行深度序列化
      if (typeof arg === 'object') {
        try {
          const serialized = JSON.parse(JSON.stringify(arg));
          console.log(`[AppWindow] Successfully serialized arg ${index} for channel ${channel}`);
          return serialized;
        } catch (error) {
          console.error(`[AppWindow] Failed to serialize argument ${index} for channel ${channel}:`, error);
          console.error(`[AppWindow] Problematic data:`, arg);
          console.error(`[AppWindow] Stack trace:`, new Error().stack);
          return '[Unserializable Object]';
        }
      }

      return arg;
    });

    try {
      this.webContents.send(channel, ...serializedArgs);
      console.log(`[AppWindow] Successfully sent to channel: ${channel}`);
    } catch (error) {
      console.error(`[AppWindow] Failed to send to channel ${channel}:`, error);
      console.error(`[AppWindow] Args:`, serializedArgs);
    }
  }

  public updateTitle() {
    const {selected} = this.viewManager;
    if (!selected) return;

    this.win.setTitle(
      selected.title.trim() === ''
        ? app.name
        : `${selected.title} - ${app.name}`,
    );
  }
}
