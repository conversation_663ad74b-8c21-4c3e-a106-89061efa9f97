import { BrowserWindow } from 'electron';
import { Application } from '@electron/main/core/application';
import {
  DIALOG_MARGIN_TOP,
  DIALOG_MARGIN,
  DIALOG_TOP,
} from '@electron/renderer/constants/design';

export const showDownloadsDialog = (
  browserWindow: BrowserWindow,
  x: number,
  y: number,
) => {
  let height = 0;
  let isInitialShow = true;

  const dialog = Application.instance.dialogs.show({
    name: 'downloads-dialog',
    browserWindow,
    getBounds: () => {
      const winBounds = browserWindow.getContentBounds();
      const maxHeight = winBounds.height - DIALOG_TOP - 16;

      height = Math.round(Math.min(winBounds.height, height + 28));

      dialog.browserView.webContents.send(
        `max-height`,
        Math.min(maxHeight, height),
      );

      return {
        x: x - 350 + DIALOG_MARGIN,
        y: y - DIALOG_MARGIN_TOP,
        width: 350,
        height,
      };
    },
    onWindowBoundsUpdate: (disposition) => {
      console.log('[DownloadsDialog] Window bounds updated:', disposition, 'isInitialShow:', isInitialShow);

      // 避免在初始显示时立即隐藏
      if (isInitialShow) {
        console.log('[DownloadsDialog] Ignoring initial bounds update');
        return;
      }

      // 只在窗口移动或调整大小时隐藏对话框
      if (disposition === 'move' || disposition === 'resize') {
        console.log('[DownloadsDialog] Hiding dialog due to window', disposition);
        dialog.hide();
      }
    },
  });

  // 延迟重置初始显示标志
  setTimeout(() => {
    isInitialShow = false;
  }, 100);

  if (!dialog) return;

  dialog.on('height', (e, h) => {
    height = h;
    dialog.rearrange();
  });
};
