import { BrowserView, ipcMain } from 'electron';
import { AppWindow } from '../ui/windows';
import { getWebUIURL } from '../utils/webui-main';

export class AIToolbarManager {
  private window: AppWindow;
  private toolbarView: BrowserView | null = null;
  private isVisible = true;
  private readonly TOOLBAR_WIDTH = 64;

  constructor(window: AppWindow) {
    this.window = window;
    this.setupIpcHandlers();
  }

  private setupIpcHandlers() {
    const { id } = this.window.win;

    // 处理AI工具栏的显示/隐藏
    ipcMain.handle(`ai-toolbar-toggle-${id}`, () => {
      return this.toggle();
    });

    // 处理AI工具按钮点击
    ipcMain.handle(`ai-toolbar-tool-click-${id}`, (e, toolId: string) => {
      return this.handleToolClick(toolId);
    });

    // 获取AI工具栏状态
    ipcMain.handle(`ai-toolbar-get-state-${id}`, () => {
      return {
        isVisible: this.isVisible,
        width: this.TOOLBAR_WIDTH,
      };
    });
  }

  public async create() {
    if (this.toolbarView) {
      return;
    }

    console.log('[AIToolbarManager] Creating AI toolbar view');

    this.toolbarView = new BrowserView({
      webPreferences: {
        nodeIntegration: true,
        nodeIntegrationInWorker: false,
        nodeIntegrationInSubFrames: false,
        contextIsolation: false,
        enableRemoteModule: true,
        webSecurity: false,
        allowRunningInsecureContent: true,
        worldSafeExecuteJavaScript: false,
      },
    });

    // 启用remote模块
    require('@electron/remote/main').enable(this.toolbarView.webContents);

    // 加载AI工具栏页面
    const toolbarUrl = await getWebUIURL('ai-toolbar');
    console.log('[AIToolbarManager] Loading toolbar URL:', toolbarUrl);
    
    await this.toolbarView.webContents.loadURL(toolbarUrl);

    // 添加到窗口
    this.window.win.addBrowserView(this.toolbarView);

    // 设置初始bounds
    this.updateBounds();

    // 监听工具栏页面的消息
    this.toolbarView.webContents.on('ipc-message', (event, channel, ...args) => {
      console.log('[AIToolbarManager] Received IPC message:', channel, args);
      
      switch (channel) {
        case 'ai-tool-clicked':
          this.handleToolClick(args[0]);
          break;
        case 'ai-toolbar-ready':
          console.log('[AIToolbarManager] Toolbar is ready');
          break;
      }
    });

    // 通知React AI工具栏已创建
    this.window.send('ai-toolbar-visibility-changed', this.isVisible);

    console.log('[AIToolbarManager] AI toolbar created successfully');
  }

  public destroy() {
    if (this.toolbarView) {
      this.window.win.removeBrowserView(this.toolbarView);
      if (!this.toolbarView.webContents.isDestroyed()) {
        this.toolbarView.webContents.destroy();
      }
      this.toolbarView = null;
    }
  }

  public toggle(): boolean {
    this.isVisible = !this.isVisible;
    
    if (this.toolbarView) {
      if (this.isVisible) {
        this.window.win.addBrowserView(this.toolbarView);
        this.updateBounds();
      } else {
        this.window.win.removeBrowserView(this.toolbarView);
      }
    }

    // 通知React更新UI布局
    this.window.send('ai-toolbar-visibility-changed', this.isVisible);

    // 通知ViewManager重新计算页面bounds
    this.window.viewManager.fixBounds();

    return this.isVisible;
  }

  public updateBounds() {
    if (!this.toolbarView || !this.isVisible) {
      return;
    }

    // 检查是否处于全屏模式
    const isFullscreen = this.window.viewManager.fullscreen;

    if (isFullscreen) {
      // 全屏模式下隐藏AI工具栏
      this.window.win.removeBrowserView(this.toolbarView);
      return;
    } else {
      // 非全屏模式下显示AI工具栏
      if (!this.window.win.getBrowserViews().includes(this.toolbarView)) {
        this.window.win.addBrowserView(this.toolbarView);
      }
    }

    const { width: windowWidth, height: windowHeight } = this.window.win.getContentBounds();

    const bounds = {
      x: 0,
      y: 0,
      width: this.TOOLBAR_WIDTH,
      height: windowHeight,
    };

    console.log('[AIToolbarManager] Setting toolbar bounds:', bounds);
    this.toolbarView.setBounds(bounds);
  }

  private async handleToolClick(toolId: string) {
    console.log('[AIToolbarManager] Tool clicked:', toolId);

    // AI工具页面URL映射
    const toolUrls = {
      notes: await getWebUIURL('ai-notes'),
      memory: await getWebUIURL('ai-memory'),
      clipboard: await getWebUIURL('ai-clipboard'),
    };

    const url = toolUrls[toolId as keyof typeof toolUrls];
    if (url) {
      // 检查是否已经有该工具的标签页
      const existingView = this.window.viewManager.findByKey(toolId);

      if (existingView) {
        // 切换到现有标签页
        await this.window.viewManager.select(existingView.id);
      } else {
        // 创建新的标签页
        this.window.viewManager.create({
          url,
          active: true,
        });
      }
    }
  }

  public get width(): number {
    // 全屏模式下或不可见时返回0
    const isFullscreen = this.window.viewManager.fullscreen;
    return (this.isVisible && !isFullscreen) ? this.TOOLBAR_WIDTH : 0;
  }

  public get visible(): boolean {
    return this.isVisible;
  }
}
