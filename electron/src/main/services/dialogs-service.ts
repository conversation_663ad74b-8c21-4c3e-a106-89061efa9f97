import { BrowserView, app, ipcMain } from 'electron';
import { join } from 'path';
import { SearchDialog } from '@electron/main/ui/dialogs/search';
import { PreviewDialog } from '@electron/main/ui/dialogs/preview';
import { PersistentDialog } from '@electron/main/ui/dialogs/dialog';
import { Application } from '@electron/main/core/application';
import { IRectangle } from '@electron/types';
import { ELECTRON_WEBUI_BASE_URL } from '@electron/renderer/constants/files';

interface IDialogTabAssociation {
  tabId?: number;
  getTabInfo?: (tabId: number) => any;
  setTabInfo?: (tabId: number, ...args: any[]) => void;
}

type BoundsDisposition = 'move' | 'resize';

interface IDialogShowOptions {
  name: string;
  browserWindow: Electron.BrowserWindow;
  hideTimeout?: number;
  devtools?: boolean;
  tabAssociation?: IDialogTabAssociation;
  onWindowBoundsUpdate?: (disposition: BoundsDisposition) => void;
  onHide?: (dialog: IDialog) => void;
  getBounds: () => IRectangle;
}

interface IDialog {
  name: string;
  browserView: BrowserView;
  id: number;
  tabIds: number[];
  _sendTabInfo: (tabId: number) => void;
  hide: (tabId?: number) => void;
  handle: (name: string, cb: (...args: any[]) => any) => void;
  on: (name: string, cb: (...args: any[]) => any) => void;
  rearrange: (bounds?: IRectangle) => void;
}

export const roundifyRectangle = (rect: IRectangle): IRectangle => {
  const newRect: any = { ...rect };
  Object.keys(newRect).forEach((key) => {
    if (!isNaN(newRect[key])) newRect[key] = Math.round(newRect[key]);
  });
  return newRect;
};

export class DialogsService {
  public browserViews: BrowserView[] = [];
  public browserViewDetails = new Map<number, boolean>();
  public dialogs: IDialog[] = [];

  public persistentDialogs: PersistentDialog[] = [];

  public run() {
    this.createBrowserView();

    this.persistentDialogs.push(new SearchDialog());
    this.persistentDialogs.push(new PreviewDialog());
  }

  private createBrowserView() {
    const view = new BrowserView({
      webPreferences: {
        nodeIntegration: true,
        nodeIntegrationInWorker: true,
        nodeIntegrationInSubFrames: true,
        contextIsolation: false,
        enableRemoteModule: true,
        webviewTag: true,
        worldSafeExecuteJavaScript: false,
        allowRunningInsecureContent: true,
      },
    });
    require("@electron/remote/main").enable(view.webContents);

    view.webContents.loadURL(`about:blank`);

    this.browserViews.push(view);

    this.browserViewDetails.set(view.webContents.id, false);

    return view;
  }

  public show(options: IDialogShowOptions): IDialog {
    const {
      name,
      browserWindow,
      getBounds,
      devtools,
      onHide,
      hideTimeout,
      onWindowBoundsUpdate,
      tabAssociation,
    } = options;

    console.log('[DialogsService] Showing dialog:', name);
    const startTime = Date.now();
    const foundDialog = this.getDynamic(name);

    let browserView = foundDialog
      ? foundDialog.browserView
      : this.browserViews.find(
          (x) => !this.browserViewDetails.get(x.webContents.id),
        );

    if (!browserView) {
      browserView = this.createBrowserView();
    }

    console.log('[DialogsService] BrowserView found/created:', !!browserView);

    const appWindow = Application.instance.windows.fromBrowserWindow(
      browserWindow,
    );

    if (foundDialog && tabAssociation) {
      foundDialog.tabIds.push(tabAssociation.tabId);
      foundDialog._sendTabInfo(tabAssociation.tabId);
    }

    browserWindow.webContents.send('dialog-visibility-change', name, true);

    this.browserViewDetails.set(browserView.webContents.id, true);

    if (foundDialog) {
      browserWindow.addBrowserView(browserView);
      foundDialog.rearrange();
      return null;
    }

    browserWindow.addBrowserView(browserView);
    console.log('[DialogsService] BrowserView added to window');
    browserView.setBounds({ x: 0, y: 0, width: 1, height: 1 });
    console.log('[DialogsService] Initial bounds set:', { x: 0, y: 0, width: 1, height: 1 });

    if (devtools) {
      browserView.webContents.openDevTools({ mode: 'detach' });
    }

    const tabsEvents: {
      activate?: (id: number) => void;
      remove?: (id: number) => void;
    } = {};

    const windowEvents: {
      resize?: () => void;
      move?: () => void;
    } = {};

    const channels: string[] = [];

    // 定义通用隐藏消息处理程序
    const genericHideHandler = (event: Electron.IpcMainEvent) => {
      // 检查发送消息的 webContents ID 是否匹配当前对话框
      if (event.sender.id === browserView.webContents.id) {
        console.log(`[DialogsService] Received generic hide-dialog message from webContents ID: ${event.sender.id}`);
        dialog.hide();
      }
    };

    const dialog: IDialog = {
      browserView,
      id: browserView.webContents.id,
      name,
      tabIds: [tabAssociation?.tabId],
      _sendTabInfo: (tabId) => {
        if (tabAssociation.getTabInfo) {
          const data = tabAssociation.getTabInfo(tabId);
          browserView.webContents.send('update-tab-info', tabId, data);
        }
      },
      hide: (tabId) => {
        const { selectedId } = appWindow.viewManager;

        dialog.tabIds = dialog.tabIds.filter(
          (x) => x !== (tabId || selectedId),
        );

        if (tabId && tabId !== selectedId) return;

        browserWindow.webContents.send('dialog-visibility-change', name, false);

        browserWindow.removeBrowserView(browserView);

        if (tabAssociation && dialog.tabIds.length > 0) return;

        ipcMain.removeAllListeners(`hide-${browserView.webContents.id}`);
        // 移除通用隐藏消息监听器
        ipcMain.removeListener('hide-dialog', genericHideHandler);
        channels.forEach((x) => {
          ipcMain.removeHandler(x);
          ipcMain.removeAllListeners(x);
        });

        this.dialogs = this.dialogs.filter((x) => x.id !== dialog.id);

        this.browserViewDetails.set(browserView.webContents.id, false);

        if (this.browserViews.length > 1) {
          // TODO: garbage collect unused BrowserViews?
          // this.browserViewDetails.delete(browserView.id);
          // browserView.destroy();
          // this.browserViews.splice(1, 1);
        } else {
          browserView.webContents.loadURL('about:blank');
        }

        if (tabAssociation) {
          appWindow.viewManager.off('activated', tabsEvents.activate);
          appWindow.viewManager.off('removed', tabsEvents.remove);
        }

        browserWindow.removeListener('resize', windowEvents.resize);
        browserWindow.removeListener('move', windowEvents.move);

        if (onHide) onHide(dialog);
      },
      handle: (name, cb) => {
        const channel = `${name}-${browserView.webContents.id}`;
        ipcMain.handle(channel, (...args) => cb(...args));
        channels.push(channel);
      },
      on: (name, cb) => {
        const channel = `${name}-${browserView.webContents.id}`;
        ipcMain.on(channel, (...args) => cb(...args));
        channels.push(channel);
      },
      rearrange: (rect) => {
        rect = rect || {};
        const bounds = {
          x: 0,
          y: 0,
          width: 0,
          height: 0,
          ...roundifyRectangle(getBounds()),
          ...roundifyRectangle(rect),
        };
        console.log('[DialogsService] Setting dialog bounds:', bounds);

        // 检查边界是否合理
        if (bounds.width <= 0 || bounds.height <= 0) {
          console.warn('[DialogsService] Invalid bounds detected:', bounds);
        }

        browserView.setBounds(bounds);

        // 确保 BrowserView 可见
        if (browserView.webContents) {
          console.log('[DialogsService] BrowserView webContents exists, URL:', browserView.webContents.getURL());
          console.log('[DialogsService] BrowserView is loading:', browserView.webContents.isLoading());
        }
      },
    };

    tabsEvents.activate = (id) => {
      const visible = dialog.tabIds.includes(id);
      browserWindow.webContents.send('dialog-visibility-change', name, visible);

      if (visible) {
        dialog._sendTabInfo(id);
        browserWindow.removeBrowserView(browserView);
        browserWindow.addBrowserView(browserView);
      } else {
        browserWindow.removeBrowserView(browserView);
      }
    };

    tabsEvents.remove = (id) => {
      dialog.hide(id);
    };

    const emitWindowBoundsUpdate = (type: BoundsDisposition) => {
      if (
        tabAssociation &&
        !dialog.tabIds.includes(appWindow.viewManager.selectedId)
      ) {
        onWindowBoundsUpdate(type);
      }
    };

    windowEvents.move = () => {
      emitWindowBoundsUpdate('move');
    };

    windowEvents.resize = () => {
      emitWindowBoundsUpdate('resize');
    };

    if (tabAssociation) {
      appWindow.viewManager.on('removed', tabsEvents.remove);
      appWindow.viewManager.on('activated', tabsEvents.activate);
    }

    if (onWindowBoundsUpdate) {
      browserWindow.on('resize', windowEvents.resize);
      browserWindow.on('move', windowEvents.move);
    }

    // 等待页面完全加载完成
    const setupDialog = () => {
      console.log('[DialogsService] Setting up dialog after page load');

      // 检查窗口中的所有 BrowserView
      const allViews = browserWindow.getBrowserViews();
      console.log('[DialogsService] Total BrowserViews in window:', allViews.length);

      // 确保对话框在最顶层
      browserWindow.removeBrowserView(browserView);
      browserWindow.addBrowserView(browserView);

      // 设置最终边界
      dialog.rearrange();

      console.log('[DialogsService] BrowserView brought to top');
      console.log('[DialogsService] Final BrowserViews count:', browserWindow.getBrowserViews().length);
      console.log('[DialogsService] Page loading status:', browserView.webContents.isLoading());

      browserView.webContents.focus();
    };

    // 监听DOM ready事件
    browserView.webContents.once('dom-ready', () => {
      console.log('[DialogsService] Dialog DOM ready');

      // 如果页面还在加载，等待加载完成
      if (browserView.webContents.isLoading()) {
        console.log('[DialogsService] Page still loading, waiting for did-finish-load');

        let setupCalled = false;
        const safeSetup = () => {
          if (!setupCalled) {
            setupCalled = true;
            setupDialog();
          }
        };

        // 等待页面加载完成
        browserView.webContents.once('did-finish-load', safeSetup);

        // 减少超时时间，如果1秒后还没加载完成，强制显示
        setTimeout(() => {
          if (!setupCalled) {
            console.log('[DialogsService] Page load timeout, forcing dialog setup');
            safeSetup();
          }
        }, 1000);
      } else {
        setupDialog();
      }
    });

    // 从Web服务器加载对话框页面
    const dialogUrl = `${ELECTRON_WEBUI_BASE_URL}${name}.html`;

    console.log('[DialogsService] Loading dialog URL:', dialogUrl);

    // 添加更多调试信息
    browserView.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
      console.error('[DialogsService] Failed to load dialog:', {
        errorCode,
        errorDescription,
        validatedURL
      });
    });

    // 添加标记避免重复执行调试脚本
    let debugScriptExecuted = false;

    browserView.webContents.on('did-finish-load', () => {
      const url = browserView.webContents.getURL();
      console.log('[DialogsService] Dialog page finished loading:', url);

      // 只在开发环境且首次加载时执行调试脚本
      if (process.env.NODE_ENV === 'development' && !debugScriptExecuted) {
        debugScriptExecuted = true;

        browserView.webContents.executeJavaScript(`
          // 只执行一次的调试检查
          if (!window.__dialogDebugInitialized) {
            window.__dialogDebugInitialized = true;

            console.log('[DialogDebug] Initial page load - document ready state:', document.readyState);
            console.log('[DialogDebug] Page title:', document.title || 'No title');
            console.log('[DialogDebug] Script tags count:', document.querySelectorAll('script').length);
            console.log('[DialogDebug] App div exists:', !!document.getElementById('app'));

            // 只添加一次错误监听器
            window.addEventListener('error', (e) => {
              console.error('[DialogDebug] JavaScript error:', e.error?.message || e.message);
            });

            window.addEventListener('unhandledrejection', (e) => {
              console.error('[DialogDebug] Unhandled promise rejection:', e.reason);
            });
          }

          'Debug initialized';
        `).then(result => {
          console.log('[DialogsService] Debug script result:', result);
        }).catch(error => {
          console.error('[DialogsService] Debug script error:', error);
        });
      }
    });

    // 优化控制台消息处理，避免重复日志
    const loggedMessages = new Set<string>();

    browserView.webContents.on('console-message', (event, level, message, line, sourceId) => {
      // 过滤重复的调试消息
      if (message.includes('[DialogDebug]')) {
        const messageKey = `${message}:${line}`;
        if (loggedMessages.has(messageKey)) {
          return; // 跳过重复的调试消息
        }
        loggedMessages.add(messageKey);

        // 限制日志缓存大小
        if (loggedMessages.size > 100) {
          loggedMessages.clear();
        }
      }

      // 只在开发环境显示详细的控制台消息
      if (process.env.NODE_ENV === 'development') {
        console.log(`[DialogConsole] ${level}: ${message} (${sourceId}:${line})`);
      } else if (level >= 2) { // 只显示警告和错误
        console.log(`[DialogConsole] ${level}: ${message}`);
      }
    });

    browserView.webContents.loadURL(dialogUrl);

    // 监听特定ID的隐藏消息
    ipcMain.on(`hide-${browserView.webContents.id}`, () => {
      console.log(`[DialogsService] Received hide-${browserView.webContents.id} message`);
      dialog.hide();
    });

    // 监听通用隐藏消息（用于轻量级对话框）
    ipcMain.on('hide-dialog', genericHideHandler);

    // 将通用处理程序添加到清理列表
    channels.push('hide-dialog');

    if (tabAssociation) {
      dialog.on('loaded', () => {
        dialog._sendTabInfo(tabAssociation.tabId);
      });

      if (tabAssociation.setTabInfo) {
        dialog.on('update-tab-info', (e, tabId, ...args) => {
          tabAssociation.setTabInfo(tabId, ...args);
        });
      }
    }

    this.dialogs.push(dialog);

    return dialog;
  }

  public getBrowserViews = () => {
    return this.browserViews.concat(
      Array.from(this.persistentDialogs).map((x) => x.browserView),
    );
  };

  public destroy = () => {
    this.getBrowserViews().forEach((x) => (x.webContents as any).destroy());
  };

  public sendToAll = (channel: string, ...args: any[]) => {
    this.getBrowserViews().forEach(
      (x) =>
        !x.webContents.isDestroyed() && x.webContents.send(channel, ...args),
    );
  };

  public get(name: string) {
    return this.getDynamic(name) || this.getPersistent(name);
  }

  public getDynamic(name: string) {
    return this.dialogs.find((x) => x.name === name);
  }

  public getPersistent(name: string) {
    return this.persistentDialogs.find((x) => x.name === name);
  }

  public isVisible = (name: string) => {
    return this.getDynamic(name) || this.getPersistent(name)?.visible;
  };
}
