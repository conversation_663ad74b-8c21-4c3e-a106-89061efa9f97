import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';

export const favicons = sqliteTable('favicons', {
  url: text('url').primaryKey(),
  data: text('data').notNull(), // Base64 encoded
  mimeType: text('mime_type').default('image/png'),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull()
});

export type Favicon = typeof favicons.$inferSelect;
export type NewFavicon = typeof favicons.$inferInsert;
