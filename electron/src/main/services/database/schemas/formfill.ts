import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';

export const formFillData = sqliteTable('form_fill_data', {
  id: text('id').primaryKey(),
  type: text('type').notNull(), // 'password', 'address'
  url: text('url').notNull(),
  favicon: text('favicon'),
  fields: text('fields').notNull(), // JSON object
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull()
});

export type FormFillData = typeof formFillData.$inferSelect;
export type NewFormFillData = typeof formFillData.$inferInsert;
