import { sqliteTable, text, integer, real } from 'drizzle-orm/sqlite-core';

export const history = sqliteTable('history', {
  id: text('id').primaryKey(),
  url: text('url').notNull(),
  title: text('title'),
  visitTime: integer('visit_time', { mode: 'timestamp' }).notNull(),
  favicon: text('favicon'),
  visitCount: integer('visit_count').default(1),
  // AI增强字段 (为未来预留)
  aiInsights: text('ai_insights'), // JSON object
  aiRelevanceScore: real('ai_relevance_score'),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull()
});

export type HistoryItem = typeof history.$inferSelect;
export type NewHistoryItem = typeof history.$inferInsert;
