import { sqliteTable, AnySQLiteColumn, index, foreignKey, text, numeric, integer, real } from "drizzle-orm/sqlite-core"
  import { sql } from "drizzle-orm"

export const bookmarks = sqliteTable("bookmarks", {
	id: text("id").primaryKey(),
	title: text("title").notNull(),
	url: text("url"),
	favicon: text("favicon"),
	isFolder: numeric("is_folder").default(sql`(FALSE)`),
	parentId: text("parent_id"),
	orderIndex: integer("order_index").default(0),
	staticType: text("static_type"),
	expanded: numeric("expanded").default(sql`(FALSE)`),
	aiTags: text("ai_tags"),
	aiSummary: text("ai_summary"),
	aiCategory: text("ai_category"),
	createdAt: integer("created_at").notNull(),
	updatedAt: integer("updated_at").notNull(),
},
(table) => {
	return {
		idxBookmarksStatic: index("idx_bookmarks_static").on(table.staticType),
		idxBookmarksParent: index("idx_bookmarks_parent").on(table.parentId),
		bookmarksParentIdBookmarksIdFk: foreignKey(() => ({
			columns: [table.parentId],
			foreignColumns: [table.id],
			name: "bookmarks_parent_id_bookmarks_id_fk"
		})),
	}
});

export const history = sqliteTable("history", {
	id: text("id").primaryKey(),
	url: text("url").notNull(),
	title: text("title"),
	visitTime: integer("visit_time").notNull(),
	favicon: text("favicon"),
	visitCount: integer("visit_count").default(1),
	aiInsights: text("ai_insights"),
	aiRelevanceScore: real("ai_relevance_score"),
	createdAt: integer("created_at").notNull(),
},
(table) => {
	return {
		idxHistoryVisitTime: index("idx_history_visit_time").on(table.visitTime),
		idxHistoryUrl: index("idx_history_url").on(table.url),
	}
});

export const favicons = sqliteTable("favicons", {
	url: text("url").primaryKey(),
	data: text("data").notNull(),
	mimeType: text("mime_type").default("image/png"),
	createdAt: integer("created_at").notNull(),
});

export const formFillData = sqliteTable("form_fill_data", {
	id: text("id").primaryKey(),
	type: text("type").notNull(),
	url: text("url").notNull(),
	favicon: text("favicon"),
	fields: text("fields").notNull(),
	createdAt: integer("created_at").notNull(),
	updatedAt: integer("updated_at").notNull(),
});

export const startupTabs = sqliteTable("startup_tabs", {
	id: text("id").primaryKey(),
	title: text("title"),
	url: text("url").notNull(),
	favicon: text("favicon"),
	isUserDefined: numeric("is_user_defined").default(sql`(FALSE)`),
	orderIndex: integer("order_index").default(0),
	createdAt: integer("created_at").notNull(),
	pinned: integer("pinned").default(0),
	windowId: integer("window_id").default(1),
});

export const permissions = sqliteTable("permissions", {
	id: text("id").primaryKey(),
	origin: text("origin").notNull(),
	permissionType: text("permission_type").notNull(),
	granted: numeric("granted").default(sql`(FALSE)`),
	createdAt: integer("created_at").notNull(),
},
(table) => {
	return {
		idxPermissionsOrigin: index("idx_permissions_origin").on(table.origin),
	}
});

export const settings = sqliteTable("settings", {
	key: text("key").primaryKey(),
	value: text("value").notNull(),
	type: text("type").notNull(),
	updatedAt: integer("updated_at").notNull(),
});