import { createClient, Client } from '@libsql/client';
import { drizzle, LibSQLDatabase } from 'drizzle-orm/libsql';
import { eq, and, or, like, desc, asc } from 'drizzle-orm';
import * as schemas from '../schemas';
import { join } from 'path';
import { existsSync, mkdirSync } from 'fs';

export class LibSQLAdapter {
  private client: Client;
  private db: LibSQLDatabase<typeof schemas>;
  private initialized = false;

  constructor() {
    // 数据库文件路径 - 存储在项目根目录的storage文件夹
    // 在开发环境中，process.cwd() 返回 electron 目录，需要向上一级到项目根目录
    const currentDir = process.cwd();
    const projectRoot = currentDir.endsWith('electron') ? join(currentDir, '..') : currentDir;
    const storageDir = join(projectRoot, 'storage');
    const dbPath = join(storageDir, 'mario-ai.db');

    // 确保storage目录存在
    if (!existsSync(storageDir)) {
      mkdirSync(storageDir, { recursive: true });
    }

    console.log(`[LibSQL] 数据库文件路径: ${dbPath}`);
    console.log(`[LibSQL] Storage目录: ${storageDir}`);

    this.client = createClient({
      url: `file:${dbPath}`
    });

    this.db = drizzle(this.client, { schema: schemas });
  }

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // 创建所有表
      await this.createTables();
      // 运行迁移
      await this.runMigrations();
      this.initialized = true;
      console.log('LibSQL数据库初始化成功');
    } catch (error) {
      console.error('LibSQL数据库初始化失败:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    // 这里会通过Drizzle的迁移系统来创建表
    // 暂时先手动创建，后续会用迁移文件
    const createTableQueries = [
      `CREATE TABLE IF NOT EXISTS bookmarks (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        url TEXT,
        favicon TEXT,
        is_folder BOOLEAN DEFAULT FALSE,
        parent_id TEXT REFERENCES bookmarks(id),
        order_index INTEGER DEFAULT 0,
        static_type TEXT,
        expanded BOOLEAN DEFAULT FALSE,
        ai_tags TEXT,
        ai_summary TEXT,
        ai_category TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )`,
      
      `CREATE TABLE IF NOT EXISTS history (
        id TEXT PRIMARY KEY,
        url TEXT NOT NULL,
        title TEXT,
        visit_time INTEGER NOT NULL,
        favicon TEXT,
        visit_count INTEGER DEFAULT 1,
        ai_insights TEXT,
        ai_relevance_score REAL,
        created_at INTEGER NOT NULL
      )`,
      
      `CREATE TABLE IF NOT EXISTS favicons (
        url TEXT PRIMARY KEY,
        data TEXT NOT NULL,
        mime_type TEXT DEFAULT 'image/png',
        created_at INTEGER NOT NULL
      )`,
      
      `CREATE TABLE IF NOT EXISTS form_fill_data (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        url TEXT NOT NULL,
        favicon TEXT,
        fields TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )`,
      
      `CREATE TABLE IF NOT EXISTS startup_tabs (
        id TEXT PRIMARY KEY,
        title TEXT,
        url TEXT NOT NULL,
        favicon TEXT,
        is_user_defined BOOLEAN DEFAULT FALSE,
        order_index INTEGER DEFAULT 0,
        created_at INTEGER NOT NULL
      )`,
      
      `CREATE TABLE IF NOT EXISTS permissions (
        id TEXT PRIMARY KEY,
        origin TEXT NOT NULL,
        permission_type TEXT NOT NULL,
        granted BOOLEAN DEFAULT FALSE,
        created_at INTEGER NOT NULL,
        UNIQUE(origin, permission_type)
      )`,
      
      `CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        type TEXT NOT NULL,
        updated_at INTEGER NOT NULL
      )`
    ];

    for (const query of createTableQueries) {
      await this.client.execute(query);
    }

    // 创建索引
    const indexQueries = [
      'CREATE INDEX IF NOT EXISTS idx_bookmarks_parent ON bookmarks(parent_id)',
      'CREATE INDEX IF NOT EXISTS idx_bookmarks_static ON bookmarks(static_type)',
      'CREATE INDEX IF NOT EXISTS idx_history_url ON history(url)',
      'CREATE INDEX IF NOT EXISTS idx_history_visit_time ON history(visit_time DESC)',
      'CREATE INDEX IF NOT EXISTS idx_permissions_origin ON permissions(origin)'
    ];

    for (const query of indexQueries) {
      await this.client.execute(query);
    }
  }

  getDatabase(): LibSQLDatabase<typeof schemas> {
    if (!this.initialized) {
      throw new Error('LibSQL数据库未初始化，请先调用initialize()');
    }
    return this.db;
  }

  async close(): Promise<void> {
    if (this.client) {
      this.client.close();
      this.initialized = false;
    }
  }

  private async runMigrations(): Promise<void> {
    try {
      // 检查startup_tabs表是否有pinned和window_id字段
      const tableInfo = await this.client.execute("PRAGMA table_info(startup_tabs)");
      const columns = tableInfo.rows.map(row => row[1]); // 列名在第二列

      if (!columns.includes('pinned')) {
        console.log('[LibSQL] 添加pinned字段到startup_tabs表');
        await this.client.execute("ALTER TABLE startup_tabs ADD COLUMN pinned INTEGER DEFAULT 0");
      }

      if (!columns.includes('window_id')) {
        console.log('[LibSQL] 添加window_id字段到startup_tabs表');
        await this.client.execute("ALTER TABLE startup_tabs ADD COLUMN window_id INTEGER DEFAULT 1");
      }

      console.log('[LibSQL] 数据库迁移完成');
    } catch (error) {
      console.error('[LibSQL] 数据库迁移失败:', error);
      throw error;
    }
  }
}
