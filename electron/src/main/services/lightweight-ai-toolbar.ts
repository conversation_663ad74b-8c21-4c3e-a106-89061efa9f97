import { BrowserWindow, ipcMain, screen } from 'electron';
import { AppWindow } from '../ui/windows';

export class LightweightAIToolbar {
  private parentWindow: AppWindow;
  private toolbarWindow: BrowserWindow | null = null;
  private currentTheme: string = 'light';
  private isVisible: boolean = true;
  private showLabels: boolean = false;
  private readonly TOOLBAR_WIDTH = 64;
  private readonly TOOLBAR_WIDTH_WITH_LABELS = 120;

  constructor(parentWindow: AppWindow) {
    this.parentWindow = parentWindow;
    this.setupIpcHandlers();
  }

  private setupIpcHandlers() {
    const { id } = this.parentWindow.win;

    // 处理工具按钮点击
    ipcMain.handle(`ai-toolbar-tool-click-${id}`, (e, toolId: string) => {
      return this.handleToolClick(toolId);
    });

    // 处理主题变化
    ipcMain.on(`ai-toolbar-theme-change-${id}`, (e, theme: string) => {
      this.updateTheme(theme);
    });

    // 获取当前主题
    ipcMain.handle(`ai-toolbar-get-theme-${id}`, () => {
      return this.currentTheme;
    });

    // 切换文字标签显示
    ipcMain.handle(`ai-toolbar-toggle-labels-${id}`, () => {
      this.showLabels = !this.showLabels;
      this.updateToolbarWidth();
      return this.showLabels;
    });
  }

  public async create() {
    if (this.toolbarWindow) {
      return;
    }

    console.log('[LightweightAIToolbar] Creating lightweight AI toolbar');

    const parentBounds = this.parentWindow.win.getBounds();

    this.toolbarWindow = new BrowserWindow({
      width: this.TOOLBAR_WIDTH,
      height: parentBounds.height,
      x: parentBounds.x,
      y: parentBounds.y,
      frame: false,
      transparent: true,
      alwaysOnTop: false,
      skipTaskbar: true,
      resizable: false,
      minimizable: false,
      maximizable: false,
      closable: false,
      focusable: false,
      show: false,

      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        enableRemoteModule: true,
      },
    });

    // 设置窗口层级
    this.toolbarWindow.setParentWindow(this.parentWindow.win);

    // 加载HTML内容
    await this.toolbarWindow.loadURL(`data:text/html,${encodeURIComponent(this.getToolbarHTML())}`);

    // 绑定事件
    this.bindEvents();

    // 显示工具栏
    this.toolbarWindow.show();

    // 通知主窗口AI工具栏已显示
    this.notifyVisibilityChange(true);

    console.log('[LightweightAIToolbar] Toolbar window created successfully');
  }

  private bindEvents() {
    if (!this.toolbarWindow) return;

    // 监听父窗口移动
    this.parentWindow.win.on('move', () => {
      this.updatePosition();
    });

    // 监听父窗口resize
    this.parentWindow.win.on('resize', () => {
      this.updatePosition();
    });

    // 监听父窗口最小化
    this.parentWindow.win.on('minimize', () => {
      this.toolbarWindow?.hide();
    });

    // 监听父窗口恢复
    this.parentWindow.win.on('restore', () => {
      this.toolbarWindow?.show();
    });

    // 监听父窗口关闭
    this.parentWindow.win.on('closed', () => {
      this.destroy();
    });

    // 监听父窗口移动和resize，使用防抖避免频繁调用
    let moveTimeout: NodeJS.Timeout;
    this.parentWindow.win.on('moved', () => {
      if (!this.parentWindow.win.isFullScreen()) {
        clearTimeout(moveTimeout);
        moveTimeout = setTimeout(() => {
          this.updatePosition();
        }, 50);
      }
    });

    let resizeTimeout: NodeJS.Timeout;
    this.parentWindow.win.on('resized', () => {
      if (!this.parentWindow.win.isFullScreen()) {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
          this.updatePosition();
        }, 50);
      }
    });
  }

  public updatePosition() {
    if (!this.toolbarWindow || !this.isVisible || this.toolbarWindow.isDestroyed()) return;

    try {
      const parentBounds = this.parentWindow.win.getBounds();
      const toolbarWidth = this.width;

      // 工具栏保持全高度，通过CSS内容布局避开控制按钮
      this.toolbarWindow.setBounds({
        x: parentBounds.x,
        y: parentBounds.y,
        width: toolbarWidth,
        height: parentBounds.height,
      });
    } catch (error) {
      console.error('[LightweightAIToolbar] Error updating position:', error);
    }
  }



  private updateToolbarWidth() {
    if (!this.toolbarWindow) return;

    // 更新工具栏位置和宽度
    this.updatePosition();

    // 通知工具栏更新布局
    this.toolbarWindow.webContents.send('update-layout', { showLabels: this.showLabels });

    // 通知主窗口更新布局
    this.parentWindow.viewManager.fixBounds();
  }

  public updateTheme(theme: string) {
    this.currentTheme = theme;
    if (this.toolbarWindow) {
      this.toolbarWindow.webContents.send('update-theme', theme);
    }
  }

  private async handleToolClick(toolId: string) {
    console.log('[LightweightAIToolbar] Tool clicked:', toolId);

    // 工具URL映射
    const toolActions = {
      notes: () => this.openNotesPage(),
      memory: () => this.openMemoryPage(),
      clipboard: () => this.openClipboardPage(),
      settings: () => this.openSettingsPage(),
    };

    const action = toolActions[toolId as keyof typeof toolActions];
    if (action) {
      await action();
    }
  }

  private async openNotesPage() {
    // 检查是否已经有笔记标签页
    const existingView = this.parentWindow.viewManager.findByKey('ai-notes');
    if (existingView) {
      await this.parentWindow.viewManager.select(existingView.id);
    } else {
      this.parentWindow.viewManager.create({
        url: 'mario-ai://ai-notes',
        active: true,
      });
    }
  }

  private async openMemoryPage() {
    const existingView = this.parentWindow.viewManager.findByKey('ai-memory');
    if (existingView) {
      await this.parentWindow.viewManager.select(existingView.id);
    } else {
      this.parentWindow.viewManager.create({
        url: 'mario-ai://ai-memory',
        active: true,
      });
    }
  }

  private async openClipboardPage() {
    const existingView = this.parentWindow.viewManager.findByKey('ai-clipboard');
    if (existingView) {
      await this.parentWindow.viewManager.select(existingView.id);
    } else {
      this.parentWindow.viewManager.create({
        url: 'mario-ai://ai-clipboard',
        active: true,
      });
    }
  }

  private async openSettingsPage() {
    const existingView = this.parentWindow.viewManager.findByKey('settings');
    if (existingView) {
      await this.parentWindow.viewManager.select(existingView.id);
    } else {
      this.parentWindow.viewManager.create({
        url: 'mario-ai://settings',
        active: true,
      });
    }
  }

  public get width(): number {
    if (!this.isVisible || this.parentWindow.win.isFullScreen()) return 0;
    return this.showLabels ? this.TOOLBAR_WIDTH_WITH_LABELS : this.TOOLBAR_WIDTH;
  }

  public handleFullScreenChange(isFullScreen: boolean) {
    if (!this.toolbarWindow) return;

    console.log('[LightweightAIToolbar] Handling fullscreen change:', isFullScreen);

    if (isFullScreen) {
      // 全屏时隐藏工具栏
      this.toolbarWindow.hide();
      this.notifyVisibilityChange(false);
    } else {
      // 退出全屏时显示工具栏，延迟更新位置避免卡死
      setTimeout(() => {
        if (this.toolbarWindow && !this.toolbarWindow.isDestroyed()) {
          this.toolbarWindow.show();
          this.updatePosition();
          this.notifyVisibilityChange(true);
        }
      }, 100);
    }
  }

  public handleMaximizeChange(isMaximized: boolean) {
    if (!this.toolbarWindow) return;

    console.log('[LightweightAIToolbar] Handling maximize change:', isMaximized);

    // 最大化/还原时更新位置
    setTimeout(() => {
      if (this.toolbarWindow && !this.toolbarWindow.isDestroyed()) {
        this.updatePosition();
      }
    }, 100);
  }

  private notifyVisibilityChange(visible: boolean) {
    // 通知主窗口AI工具栏可见性变化
    this.parentWindow.send('ai-toolbar-visibility-changed', visible);
    console.log('[LightweightAIToolbar] Visibility changed:', visible);
  }

  public destroy() {
    if (this.toolbarWindow) {
      // 通知主窗口AI工具栏已隐藏
      this.notifyVisibilityChange(false);
      this.toolbarWindow.destroy();
      this.toolbarWindow = null;
    }
  }

  private getToolbarHTML(): string {
    return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <style>
    ${this.getToolbarCSS()}
  </style>
</head>
<body data-theme="${this.currentTheme}">
  <div class="toolbar" id="toolbar">
    <!-- Logo区域 - 为macOS窗口控制按钮预留空间 -->
    <div class="logo-area">
      <div class="logo">🤖</div>
      <div class="logo-label" style="display: ${this.showLabels ? 'block' : 'none'}">AI助手</div>
    </div>

    <!-- 工具按钮区域 -->
    <div class="tools-area">
      <div class="tool-item" data-tool="notes">
        <button class="tool-btn" title="智能笔记 - 创建和管理AI增强的笔记">📝</button>
        <div class="tool-label" style="display: ${this.showLabels ? 'block' : 'none'}">笔记</div>
      </div>

      <div class="tool-item" data-tool="memory">
        <button class="tool-btn" title="AI记忆 - 存储和检索重要信息">🧠</button>
        <div class="tool-label" style="display: ${this.showLabels ? 'block' : 'none'}">记忆</div>
      </div>

      <div class="tool-item" data-tool="clipboard">
        <button class="tool-btn" title="智能剪贴板 - 管理剪贴板历史和内容">📋</button>
        <div class="tool-label" style="display: ${this.showLabels ? 'block' : 'none'}">剪贴板</div>
      </div>
    </div>

    <!-- 设置按钮区域 -->
    <div class="settings-area">
      <div class="tool-item" data-tool="settings">
        <button class="tool-btn" title="设置 - 配置AI工具和偏好">⚙️</button>
        <div class="tool-label" style="display: ${this.showLabels ? 'block' : 'none'}">设置</div>
      </div>
    </div>
  </div>

  <script>
    ${this.getToolbarJS()}
  </script>
</body>
</html>
    `;
  }

  private getToolbarCSS(): string {
    return `
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        user-select: none;
      }

      :root {
        /* Light主题 */
        --bg-primary: #ffffff;
        --bg-secondary: #f8f9fa;
        --bg-hover: rgba(0,0,0,0.05);
        --bg-active: rgba(0,0,0,0.1);
        --text-primary: #333333;
        --text-secondary: #666666;
        --border-color: #e1e5e9;
        --shadow-color: rgba(0,0,0,0.1);
      }

      [data-theme="dark"], [data-theme="wexond-dark"] {
        /* Dark主题 */
        --bg-primary: #2d2d2d;
        --bg-secondary: #3a3a3a;
        --bg-hover: rgba(255,255,255,0.1);
        --bg-active: rgba(255,255,255,0.15);
        --text-primary: #ffffff;
        --text-secondary: #cccccc;
        --border-color: #555555;
        --shadow-color: rgba(0,0,0,0.3);
      }

      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: var(--bg-primary);
        color: var(--text-primary);
        overflow: hidden;
        transition: all 0.2s ease;
      }

      .toolbar {
        width: 100%;
        height: 100vh;
        display: flex;
        flex-direction: column;
        background: var(--bg-primary);
        border-right: 1px solid var(--border-color);
        box-shadow: 2px 0 8px var(--shadow-color);
        transition: all 0.3s ease;
      }

      .logo-area {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 16px 8px 12px 8px;
        margin-top: ${process.platform === 'darwin' ? '72px' : '16px'}; /* macOS上为窗口控制按钮预留空间 */
        border-bottom: 1px solid var(--border-color);
      }

      .logo {
        font-size: 24px;
        margin-bottom: 4px;
        transition: transform 0.2s ease;
      }

      .logo:hover {
        transform: scale(1.1);
      }

      .logo-label {
        font-size: 10px;
        color: var(--text-secondary);
        text-align: center;
        font-weight: 500;
      }

      .tools-area {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 16px 8px;
      }

      .settings-area {
        padding: 8px;
        border-top: 1px solid var(--border-color);
        margin-bottom: 8px;
      }

      .tool-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
      }

      .tool-btn {
        width: 48px;
        height: 48px;
        background: var(--bg-secondary);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        cursor: pointer;
        font-size: 20px;
        color: var(--text-primary);
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
      }

      .tool-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, transparent 0%, var(--bg-hover) 100%);
        opacity: 0;
        transition: opacity 0.2s ease;
      }

      .tool-btn:hover {
        background: var(--bg-hover);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px var(--shadow-color);
      }

      .tool-btn:hover::before {
        opacity: 1;
      }

      .tool-btn:active {
        background: var(--bg-active);
        transform: translateY(0);
      }

      .tool-btn:focus {
        outline: 2px solid var(--border-color);
        outline-offset: 2px;
      }

      .tool-label {
        font-size: 10px;
        color: var(--text-secondary);
        text-align: center;
        font-weight: 500;
        white-space: nowrap;
        transition: all 0.2s ease;
      }

      /* Tooltip样式 */
      .tool-btn[title]:hover::after {
        content: attr(title);
        position: absolute;
        left: 100%;
        top: 50%;
        transform: translateY(-50%);
        margin-left: 8px;
        padding: 8px 12px;
        background: var(--text-primary);
        color: var(--bg-primary);
        border-radius: 6px;
        font-size: 12px;
        white-space: nowrap;
        z-index: 1000;
        box-shadow: 0 2px 8px var(--shadow-color);
        animation: tooltipFadeIn 0.2s ease;
      }

      @keyframes tooltipFadeIn {
        from { opacity: 0; transform: translateY(-50%) translateX(-4px); }
        to { opacity: 1; transform: translateY(-50%) translateX(0); }
      }

      /* 响应式调整 */
      @media (max-width: 120px) {
        .tool-label {
          font-size: 9px;
        }

        .logo-label {
          font-size: 9px;
        }
      }
    `;
  }

  private getToolbarJS(): string {
    return `
      const { ipcRenderer } = require('electron');
      const windowId = ${this.parentWindow.win.id};

      // 处理工具按钮点击
      document.addEventListener('click', async (e) => {
        const toolItem = e.target.closest('.tool-item');
        if (toolItem) {
          const toolId = toolItem.getAttribute('data-tool');
          if (toolId) {
            try {
              // 添加点击动画
              const btn = toolItem.querySelector('.tool-btn');
              btn.style.transform = 'scale(0.95)';
              setTimeout(() => {
                btn.style.transform = '';
              }, 150);

              await ipcRenderer.invoke(\`ai-toolbar-tool-click-\${windowId}\`, toolId);
            } catch (error) {
              console.error('Failed to handle tool click:', error);
            }
          }
        }
      });

      // 监听主题变化
      ipcRenderer.on('update-theme', (event, theme) => {
        document.body.setAttribute('data-theme', theme);
        console.log('[AI Toolbar] Theme updated to:', theme);
      });

      // 监听布局变化
      ipcRenderer.on('update-layout', (event, { showLabels }) => {
        const labels = document.querySelectorAll('.tool-label, .logo-label');
        labels.forEach(label => {
          label.style.display = showLabels ? 'block' : 'none';
        });
        console.log('[AI Toolbar] Layout updated, showLabels:', showLabels);
      });

      // 获取初始主题
      ipcRenderer.invoke(\`ai-toolbar-get-theme-\${windowId}\`).then(theme => {
        if (theme) {
          document.body.setAttribute('data-theme', theme);
        }
      });

      // 添加键盘快捷键支持
      document.addEventListener('keydown', (e) => {
        if (e.altKey) {
          switch(e.key) {
            case '1':
              document.querySelector('[data-tool="notes"] .tool-btn').click();
              break;
            case '2':
              document.querySelector('[data-tool="memory"] .tool-btn').click();
              break;
            case '3':
              document.querySelector('[data-tool="clipboard"] .tool-btn').click();
              break;
            case '0':
              document.querySelector('[data-tool="settings"] .tool-btn').click();
              break;
          }
        }
      });

      // 添加鼠标悬停效果增强
      document.querySelectorAll('.tool-btn').forEach(btn => {
        btn.addEventListener('mouseenter', () => {
          btn.style.transition = 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
        });

        btn.addEventListener('mouseleave', () => {
          btn.style.transition = 'all 0.2s ease';
        });
      });

      console.log('[AI Toolbar] Lightweight toolbar initialized with enhanced UX');
    `;
  }
}
