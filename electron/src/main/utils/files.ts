import { DIRECTORIES } from '@electron/renderer/constants/files';
import { DEFAULT_SETTINGS } from '@electron/renderer/constants/settings';
import { existsSync, mkdirSync, writeFileSync } from 'fs';
import { resolve } from 'path';
import { app } from 'electron';

const getPath = (...relativePaths: string[]) => {
  const path = app.getPath('userData');
  return resolve(path, ...relativePaths).replace(/\\/g, '/');
};

const FILES = {
  'settings.json': DEFAULT_SETTINGS,
  'window-data.json': {},
};

export const checkFiles = () => {
  for (const dir of DIRECTORIES) {
    const path = getPath(dir);
    if (!existsSync(path)) {
      mkdirSync(path);
    }
  }

  Object.keys(FILES).forEach((key) => {
    const defaultContent = (FILES as any)[key];
    const path = getPath(key);

    if (!existsSync(path)) {
      writeFileSync(path, JSON.stringify(defaultContent));
    }
  });
};

// 添加与renderer-process/utils/files.ts相同的函数以确保哈希一致性
export const pathExists = (path: string) => {
  return new Promise((resolve) => {
    const { stat } = require('fs');
    stat(path, (error: any) => {
      resolve(!error);
    });
  });
};

export const isExpire = (path: string, hour: Number) => {
  return new Promise((resolve) => {
    const { stat } = require('fs');
    stat(path, (error: any, stats: any) => {
      if (error) {
        resolve(true);
        return
      }
      const modifiedTime = stats.mtime;
      const currentTime = new Date();
      const timeDiff = currentTime.getTime() - modifiedTime.getTime();
      const hoursDiff = timeDiff / (1000 * 60 * 60);
      if (hoursDiff <= hour) {
        resolve(false);
      } else {
        resolve(true);
      }
    });
  });
}

export const deletePath = (path: string) => {
  return new Promise((resolve, reject) => {
    const { stat, rmdir, rm } = require('fs');
    stat(path, (error: any, stats: any) => {
      if(!error) {
        if(stats.isDirectory()) {
          rmdir(path, (error: any) => {
            resolve(!error);
          });
        } else {
          rm(path, (error: any) => {
            resolve(!error);
          });
        }
      } else {
        resolve(true);
      }
    });
  });
};

export const deleteDir = (path: string) => {
  return new Promise((resolve) => {
    const { rmdir } = require('fs');
    rmdir(path, (error: any) => {
      resolve(!error);
    });
  });
};

export const readFileAsText = (filePath: string) => {
  const { readFileSync } = require('fs');
  if (existsSync(filePath)) {
    try {
      return readFileSync(filePath, 'utf8');
    } catch (error) {
      console.error(`Failed to read file: ${error}`);
      return null;
    }
  } else {
    console.error('File does not exist');
    return null;
  }
}

export const writeFileText = (filePath: string, text: string) => {
    try {
      writeFileSync(filePath, text, 'utf8');
    } catch (error) {
      console.error(`Failed to write file: ${error}`);
    }
}
