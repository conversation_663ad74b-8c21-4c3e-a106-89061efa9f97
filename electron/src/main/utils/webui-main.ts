import {ELECTRON_WEBUI_BASE_URL, ELECTRON_WEBUI_URL_SUFFIX} from '@electron/renderer/constants/files';
import {Application} from "@electron/main/core/application";

export const getWebUIURL = async (hostname: string) => {
  if (hostname == "newtab" && process.env.ENABLE_EXTENSIONS) {
    try {
      const newtabUrl = await Application.instance.storage.getConfig('newtab');
      console.log("newtab", newtabUrl);
      if (newtabUrl && newtabUrl != "") {
        if(Application.instance.tempTabUrl) {
          return Application.instance.tempTabUrl;
        }
        return newtabUrl.split("\n")[0];
      }
    } catch (error) {
      console.error('[WebUI] 获取newtab配置失败:', error);
    }
  }
  return `${ELECTRON_WEBUI_BASE_URL}${hostname}${ELECTRON_WEBUI_URL_SUFFIX}`;
}
