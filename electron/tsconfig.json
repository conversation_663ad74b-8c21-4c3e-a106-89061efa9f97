{
  "compilerOptions": {
    "outDir": "./build/",
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "alwaysStrict": true,
    "skipLibCheck": true,
    "module": "CommonJS",
    "target": "es2017",
    "allowJs": true,
    "sourceMap": true,
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "lib": [
      "esnext"
    ],
    "experimentalDecorators": true,
    "baseUrl": ".",
    "paths": {
      "@electron/*": ["src/*"],
    }
  },
  "include": [
    "./**/*.ts",
    "./**/*.tsx"
  ]
}