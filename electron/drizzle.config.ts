import { defineConfig } from 'drizzle-kit';
import { join } from 'path';

// 确定数据库路径 - 与 libsql-adapter.ts 保持一致
const currentDir = process.cwd();
const projectRoot = currentDir.endsWith('electron') ? join(currentDir, '..') : currentDir;
const dbPath = join(projectRoot, 'storage', 'mario-ai.db');

export default defineConfig({
  schema: './src/main/services/database/schemas/*',
  out: './src/main/services/database/migrations',
  dialect: 'turso',
  dbCredentials: {
    url: `file:${dbPath}`
  },
  verbose: true,
  strict: true
});
