{"name": "@mario-ai/electron", "version": "1.1.2", "license": "MIT", "description": "Extensible, fast and innovative web browser with material UI - Electron Main Process", "main": "build/main.bundle.js", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "packageManager": "pnpm@8.15.0", "scripts": {"build": "vite build", "build:dev": "cross-env NODE_ENV=development DEV=1 vite build", "build:watch": "cross-env NODE_ENV=development DEV=1 vite build --watch", "start": "electron .", "start:dev": "cross-env NODE_ENV=development DEV=1 electron --trace-warnings .", "clean": "<PERSON><PERSON><PERSON> build", "dev:watch": "pnpm clean && pnpm build:dev && concurrently \"pnpm build:watch\" \"wait-on build/main.bundle.js && nodemon\"", "db:generate": "drizzle-kit generate:sqlite", "db:migrate": "drizzle-kit push:sqlite", "db:studio": "drizzle-kit studio", "compile-win32": "electron-builder -w", "compile-darwin": "electron-builder -m", "compile-linux": "electron-builder -l", "rebuild": "electron-builder install-app-deps"}, "dependencies": {"@mario-ai/shared": "workspace:*", "mobx": "6.3.2", "mobx-react-lite": "3.2.0"}, "devDependencies": {"@cliqz/adblocker-electron": "^1.26.15", "@electron/remote": "^2.1.1", "@libsql/client": "^0.15.10", "@types/better-sqlite3": "^7.6.0", "@types/chrome": "0.0.145", "@types/crypto-js": "^4.0.1", "@types/jszip": "^3.4.1", "@types/node": "15.12.5", "@types/node-fetch": "^3.0.3", "@types/rimraf": "^3.0.0", "@wexond/rpc-core": "^1.0.3", "@wexond/rpc-electron": "^1.0.3", "awesome-node-loader": "^1.1.1", "concurrently": "^8.2.2", "cross-env": "7.0.3", "cross-fetch": "^4.0.0", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.3", "electron": "28.2.4", "electron-builder": "22.11.7", "electron-chrome-extensions": "^3.10.1", "file-type": "16.5.0", "icojs": "^0.16.1", "jszip": "^3.6.0", "node-bookmarks-parser": "^2.0.0", "node-fetch": "^3.3.2", "nodemon": "^3.0.2", "pretty-bytes": "5.6.0", "rimraf": "^3.0.2", "source-map-support": "^0.5.19", "typescript": "^4.3.4", "url": "^0.11.4", "vite": "^4.4.0", "wait-on": "^8.0.3"}}