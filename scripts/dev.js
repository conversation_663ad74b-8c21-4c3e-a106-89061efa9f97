#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const os = require('os');

/**
 * Mario AI 开发环境启动脚本
 * 支持热更新：Web HMR + Electron 热重启
 * 跨平台兼容：Windows, macOS, Linux
 */

const rootDir = path.join(__dirname, '..');
const webDir = path.join(rootDir, 'web/browser');

// 平台检测
const platform = process.platform;
const isWindows = platform === 'win32';
const isMacOS = platform === 'darwin';
const isLinux = platform === 'linux';

console.log(`🚀 Mario AI 开发环境启动中... (${platform})`);

/**
 * 获取跨平台的延迟命令
 */
function getDelayCommand(seconds = 5) {
  if (isWindows) {
    // Windows: 使用 ping 命令延迟
    return `ping 127.0.0.1 -n ${seconds + 1} > nul`;
  } else {
    // Unix-like systems (macOS, Linux): 使用 sleep 命令
    return `sleep ${seconds}`;
  }
}

/**
 * 获取跨平台的空输出重定向
 */
function getNullRedirect() {
  return isWindows ? '> nul 2>&1' : '> /dev/null 2>&1';
}

/**
 * 跨平台命令执行函数
 */
async function runCommand(command, args, cwd, description) {
  return new Promise((resolve, reject) => {
    console.log(`\n📦 ${description}...`);

    // 跨平台 shell 配置
    const spawnOptions = {
      cwd,
      stdio: 'inherit',
      shell: isWindows ? 'cmd.exe' : true,
      env: {
        ...process.env,
        // 确保颜色输出在所有平台上正常工作
        FORCE_COLOR: '1',
        // Windows 下确保 UTF-8 编码
        ...(isWindows && { CHCP: '65001' })
      }
    };

    const child = spawn(command, args, spawnOptions);

    child.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ ${description} 完成`);
        resolve();
      } else {
        console.error(`❌ ${description} 失败 (code: ${code})`);
        reject(new Error(`${description} failed`));
      }
    });

    child.on('error', (error) => {
      console.error(`❌ ${description} 错误:`, error);
      reject(error);
    });
  });
}

async function main() {
  try {
    // 显示平台信息
    console.log(`📋 系统信息:`);
    console.log(`   操作系统: ${os.type()} ${os.release()}`);
    console.log(`   架构: ${os.arch()}`);
    console.log(`   Node.js: ${process.version}`);

    // 1. 先构建web/browser
    await runCommand('pnpm', ['build'], webDir, '构建 Web/Browser');

    // 2. 使用concurrently同时运行web服务器和electron
    console.log('\n🔥 启动热更新开发环境...');
    console.log('   📝 Web HMR: 自动检测端口');
    console.log('   🔄 Electron: 自动重启');
    console.log(`   💡 按 ${isWindows ? 'Ctrl+C' : 'Ctrl+C 或 Cmd+C'} 停止\n`);

    // 跨平台延迟命令
    const delayCommand = getDelayCommand(5);

    // 构建 concurrently 参数
    const concurrentlyArgs = [
      '--prefix', '[{name}]',
      '--names', 'Web,Electron',
      '--prefix-colors', 'cyan,magenta',
      '--kill-others-on-fail',
      '--restart-tries', '3',
      // Web 开发服务器
      '"pnpm --filter @mario-ai/browser dev"',
      // Electron 开发服务器（带延迟启动）
      `"${delayCommand} && pnpm --filter @mario-ai/electron run dev:watch"`
    ];

    // 跨平台 spawn 配置
    const spawnOptions = {
      cwd: rootDir,
      stdio: 'inherit',
      shell: isWindows ? 'cmd.exe' : true,
      env: {
        ...process.env,
        FORCE_COLOR: '1',
        // Windows 特定环境变量
        ...(isWindows && {
          CHCP: '65001',
          // 确保 Windows 下正确处理 Unicode
          NODE_OPTIONS: '--max-old-space-size=4096'
        }),
        // macOS/Linux 特定环境变量
        ...(!isWindows && {
          // 确保正确的终端颜色支持
          TERM: process.env.TERM || 'xterm-256color'
        })
      }
    };

    const child = spawn('npx', ['concurrently', ...concurrentlyArgs], spawnOptions);

    child.on('close', (code) => {
      console.log(`\n🛑 开发环境已停止 (code: ${code})`);
      process.exit(code);
    });

    child.on('error', (error) => {
      console.error('\n❌ 启动失败:', error);
      console.error('💡 请确保已安装 concurrently: npm install -g concurrently');
      process.exit(1);
    });

  } catch (error) {
    console.error('\n❌ 开发环境启动失败:', error.message);
    console.error('💡 请检查依赖是否正确安装: pnpm install');
    process.exit(1);
  }
}

// 跨平台进程退出处理
function setupGracefulShutdown() {
  const signals = ['SIGINT', 'SIGTERM'];

  // Windows 下添加额外的信号处理
  if (isWindows) {
    signals.push('SIGBREAK');
  }

  signals.forEach(signal => {
    process.on(signal, () => {
      console.log(`\n🛑 收到 ${signal} 信号，正在关闭开发环境...`);
      process.exit(0);
    });
  });

  // 处理未捕获的异常
  process.on('uncaughtException', (error) => {
    console.error('\n💥 未捕获的异常:', error);
    process.exit(1);
  });

  process.on('unhandledRejection', (reason, promise) => {
    console.error('\n💥 未处理的 Promise 拒绝:', reason);
    process.exit(1);
  });
}

// 设置优雅关闭
setupGracefulShutdown();

// 启动主程序
main().catch((error) => {
  console.error('\n💥 主程序异常:', error);
  process.exit(1);
});
