#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');

/**
 * Mario AI 跨平台打包脚本
 * 支持指定目标平台进行打包
 * 用法: node scripts/build-platform.js [platform]
 * 平台: win32, darwin, linux, all
 */

const rootDir = path.join(__dirname, '..');
const webDir = path.join(rootDir, 'web/browser');
const electronDir = path.join(rootDir, 'electron');

// 获取命令行参数
const targetPlatform = process.argv[2] || process.platform;

// 平台信息
const platformInfo = {
  win32: { name: 'Windows', ext: '.exe/.msi', icon: '🪟', command: 'compile-win32' },
  darwin: { name: 'macOS', ext: '.dmg/.app', icon: '🍎', command: 'compile-darwin' },
  linux: { name: 'Linux', ext: '.AppImage/.deb', icon: '🐧', command: 'compile-linux' }
};

// 显示帮助信息
function showHelp() {
  console.log(`
🚀 Mario AI 跨平台打包脚本

用法:
  node scripts/build-platform.js [platform]

支持的平台:
  win32   - Windows (${platformInfo.win32.ext})
  darwin  - macOS (${platformInfo.darwin.ext})
  linux   - Linux (${platformInfo.linux.ext})
  all     - 所有平台

示例:
  node scripts/build-platform.js win32    # 构建 Windows 版本
  node scripts/build-platform.js darwin   # 构建 macOS 版本
  node scripts/build-platform.js linux    # 构建 Linux 版本
  node scripts/build-platform.js all      # 构建所有平台
  node scripts/build-platform.js          # 构建当前平台

选项:
  --help, -h    显示此帮助信息
`);
}

// 验证目标平台
function validateTargetPlatform(platform) {
  // 检查帮助参数
  if (platform === '--help' || platform === '-h' || platform === 'help') {
    showHelp();
    process.exit(0);
  }

  if (platform === 'all') {
    return Object.keys(platformInfo);
  }

  if (!platformInfo[platform]) {
    console.error(`❌ 不支持的平台: ${platform}`);
    console.error('💡 支持的平台: win32, darwin, linux, all');
    console.error('💡 使用 --help 查看详细帮助');
    process.exit(1);
  }

  return [platform];
}

const targetPlatforms = validateTargetPlatform(targetPlatform);

console.log(`🚀 Mario AI 跨平台打包开始...`);
console.log(`📋 构建信息:`);
console.log(`   当前系统: ${os.type()} ${os.release()} (${os.arch()})`);
console.log(`   Node.js: ${process.version}`);
console.log(`   目标平台: ${targetPlatforms.map(p => platformInfo[p].icon + ' ' + platformInfo[p].name).join(', ')}`);

/**
 * 跨平台命令执行函数
 */
async function runCommand(command, args, cwd, description, allowedCodes = [0]) {
  return new Promise((resolve, reject) => {
    console.log(`\n📦 ${description}...`);
    
    const spawnOptions = {
      cwd,
      stdio: 'inherit',
      shell: process.platform === 'win32' ? 'cmd.exe' : true,
      env: {
        ...process.env,
        FORCE_COLOR: '1',
        NODE_ENV: 'production',
        ...(process.platform === 'win32' && { CHCP: '65001' })
      }
    };

    const child = spawn(command, args, spawnOptions);

    child.on('close', (code) => {
      if (allowedCodes.includes(code)) {
        console.log(`✅ ${description} 完成`);
        resolve();
      } else {
        console.error(`❌ ${description} 失败 (code: ${code})`);
        reject(new Error(`${description} failed`));
      }
    });

    child.on('error', (error) => {
      console.error(`❌ ${description} 错误:`, error);
      reject(error);
    });
  });
}

/**
 * 检查目录是否存在
 */
function checkDirectory(dir, name) {
  if (!fs.existsSync(dir)) {
    console.error(`❌ ${name} 目录不存在: ${dir}`);
    process.exit(1);
  }
  console.log(`✅ ${name} 目录检查通过`);
}

/**
 * 检查构建结果
 */
function checkBuildResult(filePath, name) {
  if (!fs.existsSync(filePath)) {
    console.error(`❌ ${name} 构建失败，文件不存在: ${filePath}`);
    process.exit(1);
  }
  console.log(`✅ ${name} 构建结果检查通过`);
}

/**
 * 跨平台文件复制
 */
async function copyWebToElectron() {
  const webDistDir = path.join(webDir, 'dist');
  const electronBuildDir = path.join(electronDir, 'build');

  console.log('\n📋 复制Web构建结果到Electron...');

  if (!fs.existsSync(electronBuildDir)) {
    fs.mkdirSync(electronBuildDir, { recursive: true });
  }

  try {
    if (process.platform === 'win32') {
      try {
        await runCommand('robocopy', [
          `"${webDistDir}"`, `"${electronBuildDir}"`,
          '/E', '/NFL', '/NDL', '/NJH', '/NJS', '/nc', '/ns', '/np'
        ], rootDir, '使用robocopy复制Web资源', [0, 1, 2, 3]);
      } catch (error) {
        await runCommand('xcopy', [
          `"${webDistDir}\\*"`, `"${electronBuildDir}\\"`,
          '/E', '/I', '/Y', '/Q'
        ], rootDir, '使用xcopy复制Web资源');
      }
    } else {
      await runCommand('cp', ['-r', `${webDistDir}/.`, electronBuildDir], 
        rootDir, '使用cp复制Web资源');
    }
  } catch (error) {
    console.log('⚠️ 系统复制命令失败，使用Node.js内置方法...');
    await copyDirectoryRecursive(webDistDir, electronBuildDir);
  }
}

/**
 * Node.js 内置递归复制目录
 */
async function copyDirectoryRecursive(src, dest) {
  const entries = fs.readdirSync(src, { withFileTypes: true });
  
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      await copyDirectoryRecursive(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  }
  console.log('✅ 使用Node.js内置方法复制完成');
}

/**
 * 构建指定平台
 */
async function buildForPlatform(platform) {
  const info = platformInfo[platform];
  console.log(`\n🎯 开始构建 ${info.icon} ${info.name} 平台...`);
  
  try {
    await runCommand('pnpm', [info.command], electronDir, `打包${info.name}应用程序`);
    console.log(`✅ ${info.name} 平台构建完成`);
    return true;
  } catch (error) {
    console.error(`❌ ${info.name} 平台构建失败:`, error.message);
    return false;
  }
}

/**
 * 显示构建结果
 */
function showBuildResults() {
  const distDir = path.join(electronDir, 'dist');
  if (!fs.existsSync(distDir)) {
    console.error('❌ 未找到输出目录');
    return;
  }

  console.log('\n🎉 打包完成！');
  console.log(`📁 输出目录: ${distDir}`);
  
  const files = fs.readdirSync(distDir);
  console.log('📋 生成的文件:');
  files.forEach(file => {
    const filePath = path.join(distDir, file);
    const stats = fs.statSync(filePath);
    if (stats.isFile()) {
      const size = (stats.size / 1024 / 1024).toFixed(2);
      console.log(`   📄 ${file} (${size} MB)`);
    } else {
      console.log(`   📁 ${file}/`);
    }
  });
}

async function main() {
  try {
    console.log('\n🔍 检查项目结构...');
    checkDirectory(webDir, 'Web/Browser');
    checkDirectory(electronDir, 'Electron');
    
    // 清理构建文件
    console.log('\n🧹 清理构建文件...');
    await runCommand('pnpm', ['clean'], rootDir, '清理所有构建文件');
    
    // 构建Web/Browser
    console.log('\n🌐 构建Web/Browser (生产环境)...');
    await runCommand('pnpm', ['build'], webDir, '构建Web/Browser');
    checkBuildResult(path.join(webDir, 'dist'), 'Web/Browser');
    
    // 构建Electron
    console.log('\n⚡ 构建Electron (生产环境)...');
    await runCommand('pnpm', ['build'], electronDir, '构建Electron');
    checkBuildResult(path.join(electronDir, 'build/main.bundle.js'), 'Electron主进程');

    // 复制Web构建结果
    await copyWebToElectron();
    
    // 构建目标平台
    const results = [];
    for (const platform of targetPlatforms) {
      const success = await buildForPlatform(platform);
      results.push({ platform, success });
    }
    
    // 显示结果
    showBuildResults();
    
    // 总结
    console.log('\n📊 构建总结:');
    results.forEach(({ platform, success }) => {
      const info = platformInfo[platform];
      const status = success ? '✅' : '❌';
      console.log(`   ${status} ${info.icon} ${info.name}`);
    });
    
    const failedCount = results.filter(r => !r.success).length;
    if (failedCount > 0) {
      console.log(`\n⚠️ ${failedCount} 个平台构建失败`);
      process.exit(1);
    }
    
  } catch (error) {
    console.error('\n❌ 打包失败:', error.message);
    process.exit(1);
  }
}

// 优雅关闭处理
const signals = ['SIGINT', 'SIGTERM'];
if (process.platform === 'win32') signals.push('SIGBREAK');

signals.forEach(signal => {
  process.on(signal, () => {
    console.log(`\n🛑 收到 ${signal} 信号，打包过程被中断`);
    process.exit(1);
  });
});

main().catch(error => {
  console.error('\n💥 主程序异常:', error);
  process.exit(1);
});
