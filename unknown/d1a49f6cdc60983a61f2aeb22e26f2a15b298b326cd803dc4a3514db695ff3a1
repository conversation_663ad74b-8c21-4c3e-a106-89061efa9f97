import * as React from 'react';
import { observer } from 'mobx-react-lite';
import { cn } from '@browser/utils/tailwind-helpers';
import { Button } from '@browser/core/components/Button';
import { ITheme } from '@mario-ai/shared';

type ClickEvent = (e: React.MouseEvent<HTMLDivElement>) => void;

export const SelectionDialog = observer(
  ({
    amount,
    visible,
    onDeleteClick,
    onCancelClick,
    theme,
  }: {
    amount: number;
    visible: boolean;
    onDeleteClick: ClickEvent;
    onCancelClick: ClickEvent;
    theme?: ITheme;
  }) => {
    // 主容器样式
    const dialogClasses = cn(
      'w-fit fixed top-4 rounded-lg overflow-hidden flex items-center',
      'py-3 px-4 pr-3 shadow-dialog will-change-opacity transition-opacity duration-150 z-[999]',
      'bg-mario-dialog',
      // 可见性
      visible ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none',
      // 响应式定位
      'left-[1392px] -translate-x-full', // 1024 + 320 + 64 - 16 = 1392
      'max-[1488px]:left-auto max-[1488px]:translate-x-0 max-[1488px]:right-20' // 1024 + 320 + 64 + 64 + 16 = 1488
    );

    // 标题样式
    const titleClasses = cn(
      'text-xs mr-1'
    );

    return (
      <div className={dialogClasses}>
        <div className={titleClasses}>
          {amount} 已选中
        </div>
        
        <Button 
          style={{ marginLeft: 16 }} 
          onClick={onDeleteClick}
        >
          删除选中
        </Button>
        
        <Button
          background={
            theme?.['dialog.lightForeground']
              ? 'rgba(255, 255, 255, 0.08)'
              : 'rgba(0, 0, 0, 0.08)'
          }
          foreground={theme?.['dialog.lightForeground'] ? 'white' : 'black'}
          style={{ marginLeft: 8 }}
          onClick={onCancelClick}
        >
          取消
        </Button>
      </div>
    );
  },
);
