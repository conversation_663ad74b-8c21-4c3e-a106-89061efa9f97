import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * 类名合并工具 - 合并并去重 Tailwind 类名
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * 条件类名工具
 */
export const conditionalClasses = {
  /**
   * 主题相关类名
   */
  theme: (lightClass: string, darkClass: string) => ({
    [lightClass]: true,
    [`dark:${darkClass}`]: true
  }),
  
  /**
   * 状态相关类名
   */
  state: (condition: boolean, trueClass: string, falseClass?: string) => ({
    [trueClass]: condition,
    ...(falseClass && { [falseClass]: !condition })
  })
};

/**
 * 常用样式组合
 */
export const commonStyles = {
  // 布局相关
  flexCenter: 'flex items-center justify-center',
  flexBetween: 'flex items-center justify-between',
  flexStart: 'flex items-center justify-start',
  flexEnd: 'flex items-center justify-end',
  flexCol: 'flex flex-col',
  flexColCenter: 'flex flex-col items-center justify-center',
  
  // 定位相关
  absoluteCenter: 'absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2',
  absoluteFull: 'absolute inset-0',
  relativeFull: 'relative w-full h-full',
  
  // 组件样式
  dialog: 'mx-4 mt-1 shadow-dialog rounded-dialog overflow-hidden relative bg-mario-dialog',
  button: 'min-w-20 h-8 px-3 flex items-center justify-center rounded cursor-pointer transition-all duration-200',
  input: 'w-full px-3 py-2 border rounded focus:outline-none focus:ring-2',
  
  // 文本相关
  textEllipsis: 'truncate',
  textCenter: 'text-center',
  
  // 交互状态
  hover: 'hover:opacity-80 transition-opacity duration-200',
  disabled: 'opacity-50 cursor-not-allowed',
  clickable: 'cursor-pointer select-none',
};

/**
 * 响应式断点工具
 */
export const breakpoints = {
  sm: '640px',
  md: '768px', 
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
};

/**
 * 主题颜色映射
 */
export const themeColors = {
  // 主要界面颜色
  titlebar: 'bg-mario-titlebar',
  toolbar: 'bg-mario-toolbar',
  dialog: 'bg-mario-dialog',
  page: 'bg-mario-page',
  
  // 文本颜色
  dialogText: 'text-mario-dialog-text',
  pageText: 'text-mario-page-text',
  tabText: 'text-mario-tab-text',
  tabSelectedText: 'text-mario-tab-selected',
  
  // 控件颜色
  control: 'bg-mario-control',
  controlHover: 'hover:bg-mario-control-hover',
  accent: 'bg-mario-accent text-white',
  
  // 分隔线
  separator: 'border-mario-dialog-separator',
  toolbarLine: 'border-mario-toolbar-line',
};
