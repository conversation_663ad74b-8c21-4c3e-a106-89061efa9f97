import { eventUtils } from '@browser/core/utils/platform-lite';
import * as React from 'react';
import store from '../../store';
import { observer } from 'mobx-react-lite';
import { ToolbarButton } from '../ToolbarButton';
import {
  ICON_FOLDER,
  ICON_PAGE,
  ICON_ARROW_RIGHT,
} from '@mario-ai/shared';
import { IBookmark } from '@mario-ai/shared';
import { cn } from '@browser/utils/tailwind-helpers';

type BookmarkProps = {
  title: string;
  url: string;
  favicon?: string;
  isFolder: boolean;
  id: string;
};

const Bookmark = observer(
  ({ title, url, favicon, isFolder, id }: BookmarkProps) => {
    const { buttonWidth } = store.bookmarkBar;

    // 获取真实的 favicon 数据，使用与书签管理页面完全相同的逻辑
    let realFavicon = favicon;
    let customFavicon = false;

    if (isFolder) {
      realFavicon = ICON_FOLDER;
      customFavicon = true;
    } else {
      // 直接使用 favicon 字段，如果为空则使用默认图标
      // 因为后端已经处理了 favicon 数据的转换
      realFavicon = favicon || ICON_PAGE;
      customFavicon = !favicon;
    }

    function onClick(event: any) {
      if (url) {
        eventUtils.send(`add-tab-${store.windowId}`, {
          url,
          active: true,
        });
      } else {
        store.bookmarkBar.showFolderDropdown(event, id);
      }
    }

    function onContextMenu(event: any) {
      store.bookmarkBar.createContextMenu(event, id);
    }

    // BookmarkButton 样式 - Tailwind 版本 (继承 Button 样式)
    const bookmarkButtonClasses = cn(
      'rounded-sm relative transition-colors mx-0.5 text-xs flex flex-row items-center',
      'p-1 w-auto',
      // hover 效果
      'hover:bg-mario-toolbar-button-hover',
      // 禁用状态
      'disabled:pointer-events-none disabled:opacity-25'
    );

    // Favicon 样式 - Tailwind 版本
    const faviconClasses = cn(
      'bg-contain bg-center bg-no-repeat h-4 w-4 ml-1 mr-1'
    );

    // Title 样式 - Tailwind 版本
    const titleClasses = cn(
      'min-w-0 max-w-[150px] whitespace-nowrap overflow-hidden text-clip pr-1'
    );

    return (
      <div
        className={bookmarkButtonClasses}
        style={{ maxWidth: `${buttonWidth}px` }}
        onClick={onClick}
        onContextMenu={onContextMenu}
      >
        <div
          className={faviconClasses}
          style={{
            backgroundImage: `url(${realFavicon})`,
            backgroundSize: '16px 16px', // 统一图标大小
            filter:
              store.theme['pages.lightForeground'] && customFavicon
                ? 'invert(100%)'
                : 'none',
          }}
        />
        <div className={titleClasses}>{title}</div>
      </div>
    );
  },
);

export const BookmarkBar = observer(() => {
  const { bookmarkBarItems: list, showOverflow } = store.bookmarkBar;

  // BookmarkBar 样式 - Tailwind 版本
  const bookmarkBarClasses = cn(
    'relative z-[100] flex flex-row items-center justify-between',
    'w-full min-h-8 px-2 pt-0 pr-1',
    // 根据主题设置背景色和边框
    store.theme.isCompact
      ? 'mt-0 bg-mario-titlebar border-b border-transparent'
      : 'mt-[-1px] bg-mario-toolbar border-b border-mario-toolbar-bottom-line',
    // 文本颜色
    'text-mario-addressbar-text'
  );

  // BookmarkSection 样式 - Tailwind 版本
  const bookmarkSectionClasses = cn(
    'flex flex-row items-center justify-start overflow-hidden'
  );

  return store.settings.object.bookmarksBar ? (
    <div className={bookmarkBarClasses}>
      <div className={bookmarkSectionClasses}>
        {list.map(({ title, url, favicon, _id, isFolder }: IBookmark) => (
          <Bookmark
            key={_id}
            id={_id}
            title={title}
            url={url}
            favicon={favicon}
            isFolder={isFolder}
          />
        ))}
      </div>
      {store.bookmarkBar.overflowItems.length > 0 && (
        <ToolbarButton icon={ICON_ARROW_RIGHT} onClick={showOverflow} />
      )}
    </div>
  ) : null;
});
