import * as React from 'react';
import { cn } from '@browser/utils/tailwind-helpers';
import { ICON_DROPDOWN } from '@mario-ai/shared';

// 基础控件样式类 - 对应原来的 Control css
export const controlClasses = cn(
  'h-8 relative border-none outline-none rounded flex items-center px-2 text-xs',
  'bg-mario-control text-mario-control-value',
  'focus:shadow-[0_0_0_2px_rgba(100,181,246,0.54)]'
);

// Input 组件
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  className?: string;
}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, ...props }, ref) => {
    return (
      <input
        ref={ref}
        className={cn(controlClasses, className)}
        spellCheck={false}
        {...props}
      />
    );
  }
);

Input.displayName = 'Input';

// Dropdown 组件
interface DropdownProps extends React.HTMLAttributes<HTMLDivElement> {
  dark?: boolean;
  className?: string;
  children?: React.ReactNode;
}

export const Dropdown = React.forwardRef<HTMLDivElement, DropdownProps>(
  ({ dark = false, className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(controlClasses, 'relative', className)}
        {...props}
      >
        {children}
        
        {/* 下拉箭头 - 对应原来的 &:after */}
        <div 
          className={cn(
            'absolute right-1 h-5 w-5 bg-center bg-no-repeat',
            dark ? 'invert' : ''
          )}
          style={{
            backgroundImage: `url(${ICON_DROPDOWN})`,
            backgroundSize: '20px',
          }}
        />
      </div>
    );
  }
);

Dropdown.displayName = 'Dropdown';
