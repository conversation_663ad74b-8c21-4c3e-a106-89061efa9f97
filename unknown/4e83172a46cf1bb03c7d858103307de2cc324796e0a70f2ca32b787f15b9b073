import * as React from 'react';
import { observer } from 'mobx-react-lite';

import { IHistoryItem } from '@mario-ai/shared';
import { ListItem } from '@browser/core/components/ListItem';
import { formatTime } from '@browser/core/utils/date';
import store from '../../store';
import { ICON_PAGE, ICON_CLOSE } from '@mario-ai/shared';
import { cn } from '@browser/utils/tailwind-helpers';

const onClick = (item: IHistoryItem) => () => {
  const index = store.selectedItems.indexOf(item._id);

  if (index === -1) {
    store.selectedItems.push(item._id);
  } else {
    store.selectedItems.splice(index, 1);
  }
};

const onRemoveClick = (item: IHistoryItem) => (
  e: React.MouseEvent<HTMLDivElement>,
) => {
  e.stopPropagation();
  store.removeItems([item._id]);
};

const onTitleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
  e.stopPropagation();
};

export default observer(({ data }: { data: IHistoryItem }) => {
  const selected = store.selectedItems.includes(data._id);

  let { favicon } = data;
  let customFavicon = false;

  if (favicon == null || favicon.trim() === '') {
    favicon = ICON_PAGE;
    customFavicon = true;
  } else {
    // 直接使用 favicon 字段，如果为空则使用默认图标
    // 因为后端已经处理了 favicon 数据的转换
    favicon = data.favicon || ICON_PAGE;
    customFavicon = !data.favicon;
  }

  // Favicon 样式 - Tailwind 版本
  const faviconClasses = cn(
    'h-4 w-4 mr-6 bg-center bg-no-repeat bg-contain'
  );

  // TitleContainer 样式 - Tailwind 版本
  const titleContainerClasses = cn(
    'flex-[2] mr-4 overflow-hidden text-ellipsis'
  );

  // Title 样式 - Tailwind 版本
  const titleClasses = cn(
    'whitespace-nowrap w-fit no-underline text-inherit',
    'hover:cursor-pointer hover:underline'
  );

  // Site 样式 - Tailwind 版本
  const siteClasses = cn(
    'flex-1 opacity-54 whitespace-nowrap overflow-hidden text-ellipsis'
  );

  // Time 样式 - Tailwind 版本
  const timeClasses = cn(
    'opacity-54 ml-4 mr-8'
  );

  // Remove 样式 - Tailwind 版本
  const removeClasses = cn(
    'h-4 w-4 cursor-pointer opacity-54 bg-center bg-no-repeat bg-contain',
    'hover:opacity-100'
  );

  return (
    <ListItem key={data._id} onClick={onClick(data)} selected={selected}>
      <div
        className={faviconClasses}
        style={{
          backgroundImage: `url(${favicon})`,
          opacity: customFavicon ? 1 : 0.54,
          filter:
            !customFavicon && store.theme['pages.lightForeground']
              ? 'invert(100%)'
              : 'none',
        }}
      />
      <div className={titleContainerClasses}>
        <a className={titleClasses} onClick={onTitleClick} href={data.url}>
          {data.title}
        </a>
      </div>
      <div className={siteClasses}>{data.url.split('/')[2]}</div>
      <div className={timeClasses}>{formatTime(new Date(data.date))}</div>
      <div
        className={removeClasses}
        onClick={onRemoveClick(data)}
        style={{
          backgroundImage: `url(${ICON_CLOSE})`,
          filter: store.theme['pages.lightForeground'] ? 'invert(100%)' : 'none',
        }}
      />
    </ListItem>
  );
});
