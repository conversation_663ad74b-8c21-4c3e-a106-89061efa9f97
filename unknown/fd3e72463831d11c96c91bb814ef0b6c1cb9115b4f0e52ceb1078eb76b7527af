import * as React from 'react';
import { observer } from 'mobx-react-lite';

import store from '../../store';
import { NavigationDrawer } from '@browser/core/components/NavigationDrawer';
import { SelectionDialog } from '@browser/core/components/SelectionDialog';
import { Container, Content, LeftContent } from '@browser/core/components/Pages';
import { GlobalNavigationDrawer } from '@browser/core/components/GlobalNavigationDrawer';
import { IBookmark } from '@mario-ai/shared';
import {
  ContextMenu,
  ContextMenuItem,
} from '@browser/core/components/ContextMenu';
import { getBookmarkTitle, addImported } from '../../utils';
import { cn } from '@browser/utils/tailwind-helpers';
import Tree from '../Tree';
import { Bookmark } from '../Bookmark';
import { eventUtils } from '@browser/core/utils/platform-lite';
import { Textfield } from '@browser/core/components/Textfield';
import { But<PERSON> } from '@browser/core/components/Button';
import {
  ICO<PERSON>_EDIT,
  ICON_TRASH,
  ICON_SAVE,
  ICON_DOWNLOAD,
  ICON_NEW_FOLDER,
} from '@mario-ai/shared';
import { WebUIStyle } from '@browser/core/styles/default-styles';

const onScroll = (e: any) => {
  const scrollPos = e.target.scrollTop;
  const scrollMax = e.target.scrollHeight - e.target.clientHeight - 256;

  if (scrollPos >= scrollMax) {
    store.itemsLoaded += store.getDefaultLoaded();
  }
};

const onCancelClick = () => {
  store.selectedItems = [];
};

const onDeleteClick = () => {
  store.deleteSelected();
};

const onRemoveClick = (item: IBookmark) => () => {
  store.removeItems([item._id]);
  store.menuVisible = false;
};

const onInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
  store.search(e.currentTarget.value);
};

const onNewFolderClick = () => {
  store.showDialog('new-folder');
};

const onEditClick = () => {
  store.showDialog('edit');
};

const onRenameClick = () => {
  store.showDialog('rename-folder');
};

const onPathItemClick = (item: IBookmark) => () => {
  if (item) {
    store.currentFolder = item._id;
  } else {
    store.currentFolder = null;
  }
};

const onImportClick = async () => {
  const res = await eventUtils.invoke('import-bookmarks');
  addImported(res);
};

const onExportClick = async () => {
  eventUtils.invoke('export-bookmarks');
};

const onContextMenuMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
  e.stopPropagation();
};

const onContainerMouseDown = () => {
  store.dialogVisible = false;
};

const onDialogMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
  e.stopPropagation();
};

const onSaveClick = () => {
  if (store.dialogContent === 'new-folder') {
    store.addItem({
      title: store.nameInputRef.current.value,
      isFolder: true,
      parent: store.currentFolder,
      children: [],
    });
  } else if (store.dialogContent === 'edit') {
    store.updateItem(store.currentBookmark._id, {
      title: store.nameInputRef.current.value,
      url: store.urlInputRef.current.value,
    });
  } else if (store.dialogContent === 'rename-folder') {
    store.updateItem(store.currentBookmark._id, {
      title: store.nameInputRef.current.value,
    });
  }

  store.dialogVisible = false;
};

const BookmarksList = observer(() => {
  const items = store.visibleItems;

  // PathView 样式 - Tailwind 版本
  const pathViewClasses = cn('mt-12 flex'); // margin-top: 48px

  // PathItem 样式 - Tailwind 版本
  const pathItemClasses = cn(
    'text-xl font-light opacity-54 mr-1 cursor-pointer',
    // after 伪元素通过 CSS-in-JS 实现
    'after:content-["/"] after:ml-1',
    // hover 效果
    'hover:opacity-100',
    // last-child 样式
    'last:opacity-100 last:cursor-default last:after:content-[""] last:after:ml-0'
  );

  return (
    <LeftContent>
      <SelectionDialog
        theme={store.theme}
        visible={store.selectedItems.length > 1}
        amount={store.selectedItems.length}
        onDeleteClick={onDeleteClick}
        onCancelClick={onCancelClick}
      />
      <div className={pathViewClasses}>
        {store.path.map((item) => (
          <div
            className={pathItemClasses}
            onClick={onPathItemClick(item)}
            key={item._id}
          >
            {getBookmarkTitle(item)}
          </div>
        ))}
      </div>
      {!!items.length && (
        <div className={cn(
          'mt-4 py-2 overflow-hidden rounded-lg first:mt-0',
          // 背景色根据主题
          store.theme['pages.lightForeground']
            ? 'bg-white/5'
            : 'bg-gray-50'
        )}>
          {items.map((data) => (
            <Bookmark data={data} key={data._id} />
          ))}
        </div>
      )}
    </LeftContent>
  );
});

export default observer(() => {
  // ✅ 重构: 添加书签数据初始化加载 (修复书签管理页面空白问题)
  React.useEffect(() => {
    console.log('[BookmarksApp] Component mounted, loading bookmarks...');
    store.load().catch(error => {
      console.error('[BookmarksApp] Failed to load bookmarks:', error);
    });
  }, []);

  let dialogTitle = 'New folder';

  if (store.dialogContent === 'edit') {
    dialogTitle = '编辑书签';
  } else if (store.dialogContent === 'rename-folder') {
    dialogTitle = '重命名文件夹';
  }

  return (
    <>
      <Container
        onMouseDown={onContainerMouseDown}
        darken={store.dialogVisible}
      >
        <WebUIStyle />
        <GlobalNavigationDrawer></GlobalNavigationDrawer>
        <NavigationDrawer title="书签" search searchValue={store.searchValue} onSearchInput={onInput}>
          <Tree />
          <div style={{ flex: 1 }} />
          <NavigationDrawer.Item
            icon={ICON_NEW_FOLDER}
            onClick={onNewFolderClick}
          >
            新文件夹
          </NavigationDrawer.Item>
          <NavigationDrawer.Item icon={ICON_DOWNLOAD} onClick={onImportClick}>
            导入
          </NavigationDrawer.Item>
          <NavigationDrawer.Item icon={ICON_SAVE} onClick={onExportClick}>
            导出
          </NavigationDrawer.Item>

        </NavigationDrawer>
        <ContextMenu
          onMouseDown={onContextMenuMouseDown}
          style={{
            top: store.menuTop,
            left: store.menuLeft,
          }}
          visible={store.menuVisible}
        >
          {store.currentBookmark && !store.currentBookmark.isFolder && (
            <ContextMenuItem onClick={onEditClick} icon={ICON_EDIT}>
              编辑
            </ContextMenuItem>
          )}
          {store.currentBookmark && store.currentBookmark.isFolder && (
            <ContextMenuItem onClick={onRenameClick} icon={ICON_EDIT}>
              重命名
            </ContextMenuItem>
          )}
          <ContextMenuItem
            onClick={onRemoveClick(store.currentBookmark)}
            icon={ICON_TRASH}
          >
            删除
          </ContextMenuItem>
        </ContextMenu>
        <Content onScroll={onScroll}>
          <BookmarksList />
        </Content>
      </Container>
      <div
        className={cn(
          'fixed w-[512px] p-4 left-1/2 top-1/2 rounded-[10px] z-[999]',
          'shadow-[0_8px_16px_rgba(0,0,0,0.24)] transition-opacity duration-200',
          'transform -translate-x-1/2 -translate-y-1/2',
          'bg-mario-dialog',
          store.theme['dialog.lightForeground'] ? 'text-white' : 'text-black',
          store.dialogVisible ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'
        )}
        onMouseDown={onDialogMouseDown}
      >
        <div className="text-base mb-4">{dialogTitle}</div>
        <Textfield
          style={{ width: '100%' }}
          dark={store.theme['dialog.lightForeground']}
          ref={store.nameInputRef}
          label="名称"
        ></Textfield>

        <Textfield
          style={{
            width: '100%',
            marginTop: 16,
            display: store.dialogContent === 'edit' ? 'block' : 'none',
          }}
          dark={store.theme['dialog.lightForeground']}
          ref={store.urlInputRef}
          label="地址"
        ></Textfield>

        <div className="float-right flex mt-6">
          <Button
            onClick={() => (store.dialogVisible = false)}
            background={
              store.theme['dialog.lightForeground']
                ? 'rgba(255, 255, 255, 0.08)'
                : 'rgba(0, 0, 0, 0.08)'
            }
            foreground={
              store.theme['dialog.lightForeground'] ? 'white' : 'black'
            }
          >
            取消
          </Button>
          <Button onClick={onSaveClick} style={{ marginLeft: 8 }}>
            保存
          </Button>
        </div>
        <div style={{ clear: 'both' }}></div>
      </div>
    </>
  );
});
