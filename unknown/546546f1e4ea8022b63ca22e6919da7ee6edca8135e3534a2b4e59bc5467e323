<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="color-scheme" content="dark light" />

    <style>
      body {
        margin: 0;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        color: rgba(0, 0, 0, 0.54);
      }

      .main {
        margin: 0 auto;
        width: calc(100% - 32px);
        max-width: 512px;
        margin: 14vh auto 0;
      }

      #main-title {
        font-size: 24px;
        font-weight: 500;
        margin-bottom: 16px;
      }

      #main-error-code {
        font-size: 12px;
      }

      #main-description {
        margin-bottom: 16px;
        line-height: 1.5rem;
      }

      ul {
        margin-top: 0;
      }

      #main-icon {
        background-repeat: no-repeat;
        width: 72px;
        height: 72px;
        margin-bottom: 40px;
      }
    </style>
  </head>

  <body>
    <div class="main">
      <div id="main-icon"></div>
      <div id="main-title"></div>
      <div id="main-description"></div>
      <div id="main-error-code"></div>
    </div>

    <script>
      document.body.style.backgroundColor =
        window.theme['pages.backgroundColor'];

      const url = window.errorURL;

      document.title = url;

      const errorCodeEl = document.getElementById('main-error-code');
      const titleEl = document.getElementById('main-title');
      const descriptionEl = document.getElementById('main-description');
      const iconEl = document.getElementById('main-icon');

      if (window.theme['pages.lightForeground']) {
        document.body.style.color = 'rgba(255, 255, 255, 0.54)';
        iconEl.style.filter = 'invert(1)';
      }
      const urlParams = new URLSearchParams(window.location.search);
      const code1 = urlParams.get('code');
      const desc = decodeURIComponent(urlParams.get('description')) + "\n\n" + decodeURIComponent(urlParams.get('validatedURL')) + "\n";
      if(code1) {
        errorCode = code1;
      } else {
        if (window.location.href.split('/').length === 4) {
          errorCode = window.location.href.split('/')[3];
        }
      }

      const pageUnresolvedIcon =
        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABIAQMAAABvIyEEAAAABlBMVEUAAABTU1OoaSf/AAAAAXRSTlMAQObYZgAAAENJREFUeF7tzbEJACEQRNGBLeAasBCza2lLEGx0CxFGG9hBMDDxRy/72O9FMnIFapGylsu1fgoBdkXfUHLrQgdfrlJN1BdYBjQQm3UAAAAASUVORK5CYII=';

      const info = {
        '-6': {
          title: 'Your file was not found',
          description: `It may have been moved or deleted.`,
          code: 'ERR_FILE_NOT_FOUND',
        },
        '-10': {
          title: 'This site can’t be reached',
          description: `The webpage at <b>${url}</b> might be temporarily down or it may have moved permanently to a new web address.`,
          code: 'ERR_ACCESS_DENIED',
        },
        '-105': {
          title: 'This site can’t be reached',
          description: `<b>${url}</b>’s server IP address could not be found.`,
          code: 'ERR_NAME_NOT_RESOLVED',
        },
        '-106': {
          title: 'No internet',
          description: `Try: <ul><li>Checking the network cables, modem, and router</li><li>Reconnecting to Wi-Fi</li></ul>`,
          code: 'ERR_INTERNET_DISCONNECTED',
        },
        '-108': {
          title: 'This site can’t be reached',
          description: `The webpage at <b>${url}</b> might be temporarily down or it may have moved permanently to a new web address.`,
          code: 'ERR_ADDRESS_INVALID',
        },
        '-109': {
          title: 'This site can’t be reached',
          description: `<b>${url}</b> is unreachable.`,
          code: 'ERR_ADDRESS_UNREACHABLE',
        },
        '-300': {
          title: 'This site can’t be reached',
          description: `The webpage at <b>${url}</b> might be temporarily down or it may have moved permanently to a new web address.`,
          code: 'ERR_INVALID_URL',
        },
        '-379': {
          title: 'This site can’t be reached',
          description: `The webpage at <b>${url}</b> might be temporarily down or it may have moved permanently to a new web address.`,
          code: 'ERR_HTTP_RESPONSE_CODE_FAILURE',
        },
      };

      const { title, description, code, icon } = info[errorCode] || { title: desc || "未知错误", description: desc || "未知错误", code: errorCode + ""};
      titleEl.textContent = title;
      descriptionEl.innerHTML = description;
      errorCodeEl.textContent = code;
      iconEl.style.backgroundImage = `url(${pageUnresolvedIcon})`;
    </script>
  </body>
</html>
