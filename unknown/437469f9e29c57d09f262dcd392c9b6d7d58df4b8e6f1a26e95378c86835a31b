import {
  observable,
  action,
  computed,
  makeObservable,
  makeAutoObservable,
} from 'mobx';
import * as React from 'react';

import {ITab, ITabGroup} from '../models';

import {
  TAB_ANIMATION_DURATION,
  TABS_PADDING,
  TAB_MAX_WIDTH,
} from '@browser/views/app/constants';

import store from '.';
import { eventUtils } from '@browser/core/utils/platform-lite';
import {TOOLBAR_HEIGHT} from '@mario-ai/shared';
import {TabEvent} from '@mario-ai/shared';
import {getWebUIURL} from '@browser/core/utils/webui';

export class TabsStore {
  public isDragging = false;

  public hoveredTabId = -1;

  public list: ITab[] = [];

  public selectedTabId = -1;

  public removedTabs = 0;

  public lastScrollLeft = 0;
  public lastMouseX = 0;
  public mouseStartX = 0;
  public tabStartX = 0;

  private scrollTimeout: any;

  public scrollingToEnd = false;
  public scrollable = false;

  public closedUrl = '';

  public canShowPreview = true;

  public containerRef = React.createRef<HTMLDivElement>();

  public leftMargins = 0;

  public get selectedTab() {
    return this.getTabById(this.selectedTabId);
  }

  public get hoveredTab() {
    return this.getTabById(this.hoveredTabId);
  }

  public constructor() {
    makeObservable(this, {
      list: observable,
      isDragging: observable,
      hoveredTabId: observable,
      selectedTabId: observable,
      selectedTab: computed,
      hoveredTab: computed,
      createTab: action,
      addTab: action,
      removeTab: action,
    });

    window.addEventListener('mouseup', this.onMouseUp);
    window.addEventListener('mousemove', this.onMouseMove);
    window.addEventListener('resize', this.onResize);

    eventUtils.on('tabs-resize', () => {
      this.updateTabsBounds(true);
    });

    eventUtils.on(
      'create-tab',
      (
        e,
        options: chrome.tabs.CreateProperties,
        isNext: boolean,
        id: number,
      ) => {
        if (isNext && this.selectedTab) {
          const index = this.list.indexOf(this.selectedTab) + 1;
          options.index = index;
        }

        this.createTab(options, id);
      },
    );

    eventUtils.on('select-next-tab', () => {
      if (this.selectedTab) {
        const i = this.list.indexOf(this.selectedTab);
        const nextTab = this.list[i + 1];

        if (!nextTab) {
          if (this.list[0]) {
            this.list[0].select();
          }
        } else {
          nextTab.select();
        }
      }
    });

    eventUtils.on('select-tab-index', (e, i) => {
      this.list[i]?.select();
    });

    eventUtils.on('select-last-tab', () => {
      this.list[this.list.length - 1]?.select();
    });

    eventUtils.on('select-previous-tab', () => {
      if (this.selectedTab) {
        const i = this.list.indexOf(this.selectedTab);
        const prevTab = this.list[i - 1];

        if (!prevTab) {
          if (this.list[this.list.length - 1]) {
            this.list[this.list.length - 1].select();
          }
        } else {
          prevTab.select();
        }
      }
    });

    eventUtils.on('remove-tab', (e, id: number) => {
      this.getTabById(id)?.close();
    });

    eventUtils.on('tab-event', (e, event: TabEvent, tabId, args) => {
      const tab = this.getTabById(tabId);

      if (tab) {
        if (event === 'blocked-ad') {
          tab.blockedAds.push({request: args[0], result: args[1]});
          tab.filtersCount = args[2];
        }
        if (event === 'title-updated') tab.title = args[0];
        if (event === 'favicon-updated') tab.setFavicon(args[0]);
        if (event === 'did-navigate') tab.setFavicon('');
        if (event === 'media-playing') tab.setIsPlaying(true);
        if (event === 'media-paused') tab.setIsPlaying(false);
        if (event === 'loading') tab.setLoading(args[0]);
        if (event === 'pinned') tab.setIsPinned(args[0]);
        if (event === 'credentials') tab.setHasCredentials(args[0]);
        if (event === 'xiu-video') tab.setVideoUrls(args[0] || []);

        if (event === 'url-updated') {
          const [url] = args;
          tab.url = url;
          tab.updateData();

          // 更新地址栏值，但只有在地址栏没有焦点时才更新
          if (tab.id === this.selectedTabId && !store.addressbarFocused) {
            // 设置为实际的URL而不是null
            tab.addressbarValue = url;
            store.addressbarValue = url;
            // 更新URL分段显示
            store.addressbarUrlSegments = this.parseUrlSegments(url);
          }
        }

        if (event === 'load-commit') {
          const [, , isMainFrame] = args;
          if (isMainFrame) {
            tab.blockedAds = [];
          }
        }
      }
    });

    eventUtils.on('revert-closed-tab', () => {
      this.revertClosed();
    });

    eventUtils.on('get-search-tabs', () => {
      eventUtils.send(
        'get-search-tabs',
        this.list.map((tab) => ({
          favicon: tab.favicon,
          url: tab.url,
          title: tab.title,
          id: tab.id,
        })),
      );
    });
  }

  @action
  public onResize = (e: Event) => {
    if (e.isTrusted) {
      this.removedTabs = 0;
      this.updateTabsBounds(false);
    }
  };

  public get containerWidth() {
    if (this.containerRef.current) {
      return this.containerRef.current.offsetWidth;
    }
    return 0;
  }

  public getTabById(id: number) {
    return this.list.find((x) => x.id === id);
  }

  @action
  public createTab(
    options: chrome.tabs.CreateProperties,
    id: number,
    tabGroupId = -1,
  ) {
    this.removedTabs = 0;

    const tab = new ITab(options, id);

    tab.tabGroupId = tabGroupId;

    if (options.index !== undefined) {
      this.list.splice(options.index, 0, tab);
    } else {
      this.list.push(tab);
    }

    requestAnimationFrame(() => {
      tab.setLeft(tab.getLeft(), false);
      this.updateTabsBounds(true);
      this.scrollToEnd(TAB_ANIMATION_DURATION);

      // 额外延迟确保新增按钮位置正确
      setTimeout(() => {
        this.updateTabsBounds(false);
      }, 100);
    });
    store.startupTabs.updateStartupTabItem(tab);
    return tab;
  }

  @action
  public createTabs(
    options: chrome.tabs.CreateProperties[],
    ids: number[],
  ) {
    this.removedTabs = 0;

    // 添加null检查，确保ids数组存在且长度匹配
    if (!ids || !Array.isArray(ids)) {
      console.error('[TabsStore] createTabs: ids is null or not an array:', ids);
      return [];
    }

    if (ids.length !== options.length) {
      console.error('[TabsStore] createTabs: ids length mismatch:', ids.length, 'vs', options.length);
      return [];
    }

    const tabs = options.map((option, i) => {
      const tab = new ITab(option, ids[i]);
      this.list.push(tab);
      return tab;
    });

    requestAnimationFrame(() => {
      this.updateTabsBounds(false);
      if (this.scrollable) {
        this.containerRef.current.scrollLeft =
          this.containerRef.current.scrollWidth;
      }
    });
    for (let tab of tabs) {
      store.startupTabs.updateStartupTabItem(tab);
    }

    return tabs;
  }

  public scrollToEnd = (milliseconds: number) => {
    if (!this.scrollable) return;

    const frame = () => {
      if (!this.scrollingToEnd) return;
      try {
        this.containerRef.current.scrollLeft =
          this.containerRef.current.scrollWidth;
      } catch (e) {
        console.log(e);
      }
      requestAnimationFrame(frame);
    };

    if (!this.scrollingToEnd) {
      this.scrollingToEnd = true;
      frame();
    }

    clearTimeout(this.scrollTimeout);

    this.scrollTimeout = setTimeout(() => {
      this.scrollingToEnd = false;
    }, milliseconds);
  };

  @action
  public async addTab(
    options: { url: string, active: boolean } = {url: null, active: true},
    tabGroupId: number = undefined,
  ) {
    const defaultTabOptions = {
      url: getWebUIURL("newtab"),
      active: true
    }
    if (!options.url) {
      options.url = defaultTabOptions.url;
    }
    eventUtils.send(`hide-window-${store.windowId}`);

    const opts = {...defaultTabOptions, ...options};

    const id: number = await eventUtils.invoke(
      `view-create-${store.windowId}`,
      opts,
    );
    return this.createTab(opts, id, tabGroupId);
  }

  @action
  public async addTabs(options: chrome.tabs.CreateProperties[]) {
    //eventUtils.send(`hide-window-${store.windowId}`);
    let activeCount = options.filter(it => it.active).length;
    if(activeCount != 1) {
      for (let i = 0; i < options.length; i++) {
        if (i === options.length - 1) {
          options[i].active = true;
        } else {
          options[i].active = false;
        }
      }
    }

    const ids = await eventUtils.invoke(
      `views-create-${store.windowId}`,
      options,
    );
    return this.createTabs(options, ids);
  }

  public removeTab(id: number) {
    (this.list as any).remove(this.getTabById(id));
    store.startupTabs.removeStartupTabItem(id);
  }

  // 解析URL为分段显示
  public parseUrlSegments(url: string): any[] {
    if (!url || url === '') return [];

    try {
      const urlObj = new URL(url);
      const segments = [];

      // 协议部分（灰色显示）
      segments.push({
        value: urlObj.protocol + '//',
        grayOut: true
      });

      // 域名部分（正常显示）
      segments.push({
        value: urlObj.hostname,
        grayOut: false
      });

      // 端口（如果有，灰色显示）
      if (urlObj.port) {
        segments.push({
          value: ':' + urlObj.port,
          grayOut: true
        });
      }

      // 路径（灰色显示）
      if (urlObj.pathname !== '/') {
        segments.push({
          value: urlObj.pathname,
          grayOut: true
        });
      }

      // 查询参数（灰色显示）
      if (urlObj.search) {
        segments.push({
          value: urlObj.search,
          grayOut: true
        });
      }

      // 锚点（灰色显示）
      if (urlObj.hash) {
        segments.push({
          value: urlObj.hash,
          grayOut: true
        });
      }

      return segments;
    } catch (error) {
      // 如果URL解析失败，返回原始字符串
      return [{
        value: url,
        grayOut: false
      }];
    }
  }

  @action
  public pinTab(tab: ITab) {
    tab.isPinned = true;
    store.startupTabs.updateStartupTabItem(tab);
    requestAnimationFrame(() => {
      tab.setLeft(0, false);
      this.getTabsToReplace(tab, 'left');
      this.updateTabsBounds(true);
    });
  }

  @action
  public unpinTab(tab: ITab) {
    tab.isPinned = false;
    store.startupTabs.updateStartupTabItem(tab);
    requestAnimationFrame(() => {
      tab.setLeft(
        Math.max(
          ...this.list.map(function (item) {
            return item.left;
          }),
        ) + TAB_MAX_WIDTH,
        false,
      );
      this.getTabsToReplace(tab, 'right');
      this.updateTabsBounds(true);
    });
  }

  @action
  public muteTab(tab: ITab) {
    eventUtils.send(`mute-view-${store.windowId}`, tab.id);
    tab.isMuted = true;
  }

  @action
  public unmuteTab(tab: ITab) {
    eventUtils.send(`unmute-view-${store.windowId}`, tab.id);
    tab.isMuted = false;
  }

  @action
  public updateTabsBounds(animation: boolean) {
    this.calculateTabMargins();
    this.setTabsWidths(animation);
    this.setTabGroupsLefts(animation);
    this.setTabsLefts(animation);
  }

  @action
  public calculateTabMargins() {
    const tabs = this.list.filter((x) => !x.isClosing);

    let currentGroup: number;

    this.leftMargins = 0;

    for (const tab of tabs) {
      tab.marginLeft = 0;

      if (tab.tabGroupId !== currentGroup) {
        if (tab.tabGroup) {
          tab.marginLeft = tab.tabGroup.placeholderRef.current.offsetWidth + 16;
        } else {
          tab.marginLeft = 2; // 减少左边距从6px到2px
        }

        currentGroup = tab.tabGroupId;
      }

      this.leftMargins += tab.marginLeft;
    }
  }

  @action
  public setTabGroupsLefts(animation: boolean) {
    const tabs = this.list.filter((x) => !x.isClosing);

    let left = 0;
    let currentGroup: number;

    for (const tab of tabs) {
      const group = tab.tabGroup;
      if (tab.tabGroupId !== currentGroup) {
        if (group) {
          group.setLeft(left + 8, animation && !tab.tabGroup.isNew);
          group.isNew = false;
        }

        left += tab.marginLeft;

        currentGroup = tab.tabGroupId;
      }

      left += tab.width + TABS_PADDING;
    }
  }

  @action
  public setTabsWidths(animation: boolean) {
    const tabs = this.list.filter((x) => !x.isClosing);

    const containerWidth = this.containerWidth;
    let currentGroup: ITabGroup;

    for (const tab of tabs) {
      const width = tab.getWidth(containerWidth, tabs);
      tab.setWidth(width, animation);
      const group = tab.tabGroup;

      if (group) {
        if (group !== currentGroup) {
          if (currentGroup) {
            currentGroup.setWidth(currentGroup.width, animation);
          }
          group.width = tab.marginLeft - 8 - TABS_PADDING;
          currentGroup = group;
        }
        group.width = group.width + width + TABS_PADDING;
      }

      this.scrollable = width === 72;
    }

    if (currentGroup) {
      currentGroup.setWidth(currentGroup.width, animation);
    }
  }

  @action
  public setTabsLefts(animation: boolean) {
    const tabs = this.list.filter((x) => !x.isClosing);

    const {containerWidth} = store.tabs;

    let left = 0;

    console.log('[setTabsLefts] 开始计算位置 - containerWidth:', containerWidth, 'tabs count:', tabs.length);

    for (const tab of tabs) {
      left += tab.marginLeft;

      if (!tab.isDragging) {
        tab.setLeft(left, animation);
      }

      left += tab.width + TABS_PADDING;
      console.log(`[setTabsLefts] Tab ${tab.id} - marginLeft: ${tab.marginLeft}, width: ${tab.width}, left: ${left}`);
    }

    const addTabLeft = Math.min(left, containerWidth + TABS_PADDING);
    store.addTab.setLeft(addTabLeft, animation);
  }

  @action
  public setTabToGroup(tab: ITab, tabGroupId: number) {
    const tabs = [...this.list];

    const tabIndex = tabs.indexOf(tab);
    const groupFirstTabIndex = tabs.findIndex(
      (x) => x.tabGroupId === tabGroupId,
    );

    const groupLastTab = tabs
      .slice()
      .reverse()
      .find((x) => x.tabGroupId === tabGroupId);
    const groupLastTabIndex = tabs.indexOf(groupLastTab);

    const groupTabIndex =
      tabIndex < groupFirstTabIndex
        ? groupFirstTabIndex - 1
        : groupLastTabIndex + 1;

    tab.tabGroupId = tabGroupId;
    tabs.splice(tabIndex, 1);
    tabs.splice(groupTabIndex, 0, tab);
    this.list = tabs;
    this.updateTabsBounds(false);
  }

  @action
  public replaceTab(firstTab: ITab, secondTab: ITab) {
    const index = this.list.indexOf(secondTab);

    const list = [...this.list];

    list[this.list.indexOf(firstTab)] = secondTab;
    list[index] = firstTab;

    this.list = list;

    firstTab.updateData();
    secondTab.updateData();

    this.updateTabsBounds(true);
  }

  public getTabsToReplace(callingTab: ITab, direction: string) {
    const tabs = this.list;
    const index = tabs.indexOf(callingTab);

    const {tabGroup} = callingTab;
    if (tabGroup) {
      if (
        callingTab.left < tabGroup.left ||
        callingTab.left + callingTab.width >=
        tabGroup.left + tabGroup.width + 20
      ) {
        callingTab.removeFromGroup();
        return;
      }
    }

    if (direction === 'left') {
      for (let i = index - 1; i >= 0; i--) {
        const tab = tabs[i];

        if (callingTab.isPinned && !tab.isPinned) break;

        const {tabGroup} = tab;

        if (tabGroup) {
          const tabGroupTabs = tab.tabGroup.tabs;
          const lastTab = tabGroupTabs[tabGroupTabs.length - 1];

          if (
            callingTab.tabGroupId !== tab.tabGroupId &&
            callingTab.left <= lastTab.left + lastTab.width * 0.75
          ) {
            callingTab.tabGroupId = tab.tabGroupId;
            this.updateTabsBounds(true);
          }
        }

        if (callingTab.left <= tab.width / 2 + tab.left) {
          this.replaceTab(tabs[i + 1], tab);
        } else {
          break;
        }
      }
    } else if (direction === 'right') {
      for (let i = index + 1; i < tabs.length; i++) {
        const tab = tabs[i];

        if (callingTab.isPinned && !tab.isPinned) break;

        const {tabGroup} = tab;

        if (tabGroup) {
          const tabGroupTabs = tab.tabGroup.tabs;
          const firstTab = tabGroupTabs[0];

          if (
            callingTab.tabGroupId !== tab.tabGroupId &&
            callingTab.left + callingTab.width >= firstTab.left
          ) {
            callingTab.tabGroupId = tab.tabGroupId;
            this.updateTabsBounds(true);
          }
        }

        if (callingTab.left + callingTab.width >= tab.width / 2 + tab.left) {
          this.replaceTab(tabs[i - 1], tab);
        } else {
          break;
        }
      }
    }
  }

  @action
  public onMouseUp = () => {
    const selectedTab = this.selectedTab;

    this.isDragging = false;

    if (selectedTab) {
      selectedTab.isDragging = false;
    }

    this.updateTabsBounds(true);
  };

  @action
  public onMouseMove = (e: any) => {
    const {selectedTab} = this;

    if (this.isDragging) {
      const container = this.containerRef;
      const {tabStartX, mouseStartX, lastMouseX, lastScrollLeft} = store.tabs;

      const boundingRect = container.current.getBoundingClientRect();

      if (Math.abs(e.pageX - mouseStartX) < 5) {
        return;
      }

      store.canOpenSearch = false;

      selectedTab.isDragging = true;

      const newLeft =
        tabStartX +
        e.pageX -
        mouseStartX -
        (lastScrollLeft - container.current.scrollLeft);

      let left = Math.max(0, newLeft);

      if (
        newLeft + selectedTab.width >
        container.current.scrollLeft +
        container.current.offsetWidth -
        TABS_PADDING +
        20
      ) {
        left =
          container.current.scrollLeft +
          container.current.offsetWidth -
          selectedTab.width -
          TABS_PADDING +
          20;
      }

      selectedTab.setLeft(left, false);

      if (
        e.pageY > TOOLBAR_HEIGHT + 16 ||
        e.pageY < -16 ||
        e.pageX < boundingRect.left ||
        e.pageX - boundingRect.left > store.addTab.left
      ) {
        // TODO: Create a new window
      }

      this.getTabsToReplace(
        selectedTab,
        lastMouseX - e.pageX >= 1 ? 'left' : 'right',
      );

      this.lastMouseX = e.pageX;
    }
  };

  public revertClosed() {
    this.addTab({active: true, url: this.closedUrl});
  }
}
