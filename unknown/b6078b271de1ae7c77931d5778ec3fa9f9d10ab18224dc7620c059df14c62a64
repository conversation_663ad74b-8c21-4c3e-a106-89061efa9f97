import React from 'react';
import { ITheme } from '@mario-ai/shared';

// 引入 Tailwind CSS 样式
import '@browser/styles/tailwind-theme.css';

// 基础样式类 - Tailwind 版本
const baseClasses = [
  'select-none',
  'cursor-default',
  'm-0',
  'p-0',
  'w-screen',
  'h-screen',
  'overflow-hidden',
  'box-border'
].join(' ');

// UI样式组件 - 替代 createGlobalStyle
export const UIStyle: React.FC = () => {
  React.useEffect(() => {
    // 应用基础样式到 body
    document.body.className = `${baseClasses} font-system`;

    // 应用全局样式到所有元素
    const style = document.createElement('style');
    style.textContent = `
      * {
        box-sizing: border-box;
      }
      body {
        font-family: system-ui, sans-serif;
      }
    `;
    document.head.appendChild(style);

    return () => {
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };
  }, []);

  return null;
};

// WebUI样式组件 - 替代 createGlobalStyle，支持主题
export const WebUIStyle: React.FC<{ theme?: ITheme }> = ({ theme }) => {
  React.useEffect(() => {
    // 应用基础样式到 body，但允许垂直滚动
    const webUIClasses = [
      'select-none',
      'cursor-default',
      'm-0',
      'p-0',
      'w-screen',
      'h-screen',
      'overflow-y-auto', // WebUI 允许垂直滚动
      'box-border'
    ].join(' ');

    document.body.className = `${webUIClasses} font-roboto text-body2`;

    // 应用主题相关的样式
    if (theme) {
      document.body.style.backgroundColor = theme['pages.backgroundColor'];
      document.body.style.color = theme['pages.textColor'];
    }

    // 应用全局样式
    const style = document.createElement('style');
    style.textContent = `
      * {
        box-sizing: border-box;
      }
      body {
        font-family: Roboto, system-ui, sans-serif;
        font-weight: 400;
        letter-spacing: ${14 * 0.25 / 14}rem;
        font-size: 14px;
      }
    `;
    document.head.appendChild(style);

    return () => {
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
      // 清理内联样式
      document.body.style.backgroundColor = '';
      document.body.style.color = '';
    };
  }, [theme]);

  return null;
};