import * as React from 'react';
import { cn } from '@browser/utils/tailwind-helpers';

interface Props {
  background?: string;
  foreground?: string;
  type?: 'contained' | 'outlined';
  children?: any;
  onClick?: (e: React.MouseEvent<HTMLDivElement>) => void;
  style?: any;
  className?: string;
}

export const Button = ({
  background,
  foreground,
  type = 'contained',
  onClick,
  children,
  style,
  className
}: Props) => {
  const [isHovered, setIsHovered] = React.useState(false);

  // 基础样式类
  const baseClasses = cn(
    // 布局和尺寸
    'min-w-20 w-fit h-8 px-3',
    'flex items-center justify-center',
    'overflow-hidden rounded relative cursor-pointer',
    // 过渡效果
    'transition-all duration-200'
  );

  // 动态样式
  const dynamicStyles: React.CSSProperties = {
    ...style,
    color: foreground || '#fff',
    border: type === 'outlined'
      ? `1px solid ${background || '#2196F3'}`
      : 'unset',
    backgroundColor: type === 'outlined'
      ? 'transparent'
      : background || '#2196F3',
  };

  return (
    <div
      className={cn(baseClasses, 'button', className)}
      onClick={onClick}
      style={dynamicStyles}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Hover 效果层 */}
      <div
        className={cn(
          'absolute inset-0 z-0 transition-opacity duration-200',
          isHovered ? 'opacity-[0.12]' : 'opacity-0'
        )}
        style={{ backgroundColor: foreground || '#fff' }}
      />

      {/* 标签内容 */}
      <div className="z-[1] text-xs pointer-events-none">
        {children}
      </div>
    </div>
  );
};
