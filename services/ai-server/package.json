{"name": "@mario-ai/ai-server", "version": "1.0.0", "description": "MarioAI Node.js AI服务", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "lint": "eslint src --ext .ts", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["ai", "server", "mario-ai", "express", "websocket"], "author": "MarioAI Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "ws": "^8.14.2", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "@mario-ai/shared": "workspace:*"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/ws": "^8.5.10", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/node": "^20.10.0", "tsx": "^4.6.0", "typescript": "^5.3.0", "rimraf": "^5.0.5", "jest": "^29.7.0", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0"}}