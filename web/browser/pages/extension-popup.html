<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="color-scheme" content="dark light" />

    <style>
      body {
        user-select: none;
        cursor: default;
        margin: 0;
        padding: 0;
        height: 100vh;
        width: 100vw;
        overflow: hidden;
      }

      * {
        box-sizing: border-box;
      }

      .app {
        margin: 3px 16px 16px 16px;
        max-height: 500px;
        box-shadow: 0 12px 16px rgba(0, 0, 0, 0.12),
          0 8px 10px rgba(0, 0, 0, 0.16);
        position: relative;
        overflow: auto;
        border-radius: 8px;
        transition: 0.3s opacity;
        background-color: white;
        opacity: 0;
      }
      /* 滚动条的样式 */
      .app::-webkit-scrollbar {
        width: 10px;
        height: 10px;
      }

      .app::-webkit-scrollbar-thumb {
        background-color: #aaa;
        border-radius: 5px;
      }
      
      #container {
        height: 100%;
        overflow: hidden;
      }

      .visible {
        opacity: 1;
      }
    </style>
  </head>

  <body>
    <div id="app" class="app">
      <div id="container"></div>
    </div>

    <script type="module" src="../src/views/extension-popup/index.tsx"></script>
  </body>
</html>
