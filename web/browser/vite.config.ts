import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'
import { readFileSync } from 'fs'

// 自定义插件：处理开发模式下的多入口点路由
const multiEntryPlugin = () => {
  return {
    name: 'multi-entry-dev',
    configureServer(server: any) {
      server.middlewares.use((req: any, res: any, next: any) => {
        // 处理 /app.html 路由
        if (req.url === '/app.html') {
          req.url = '/pages/app.html';
        }
        // 处理其他页面路由
        else if (req.url.match(/^\/[^\/]+\.html$/)) {
          const pageName = req.url.substring(1); // 移除开头的 /
          req.url = `/pages/${pageName}`;
        }
        next();
      });
    }
  };
};

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), multiEntryPlugin()],
  // 使用相对路径而不是绝对路径
  base: './',
  resolve: {
    alias: {
      '@browser': resolve(__dirname, './src/'),
    },
  },
  optimizeDeps: {
    exclude: ['electron']
  },
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
    'process.env.ENABLE_EXTENSIONS': JSON.stringify(process.env.ENABLE_EXTENSIONS || 'true'),
    'process.env.ENABLE_AUTOFILL': JSON.stringify(process.env.ENABLE_AUTOFILL || 'true'),
    'process.env.VERSION_CODE': JSON.stringify('1.1.2'),
  },
  build: {
    outDir: 'dist',
    rollupOptions: {
      input: {
        // 主入口
        main: resolve(__dirname, 'index.html'),
        
        // 各个页面入口
        app: resolve(__dirname, 'pages/app.html'),
        'extension-popup': resolve(__dirname, 'pages/extension-popup.html'),
        search: resolve(__dirname, 'pages/search.html'),
        preview: resolve(__dirname, 'pages/preview.html'),
        menu: resolve(__dirname, 'pages/menu.html'),
        settings: resolve(__dirname, 'pages/settings.html'),
        history: resolve(__dirname, 'pages/history.html'),
        bookmarks: resolve(__dirname, 'pages/bookmarks.html'),
        newtab: resolve(__dirname, 'pages/newtab.html'),
        auth: resolve(__dirname, 'pages/auth.html'),
        find: resolve(__dirname, 'pages/find.html'),
        permissions: resolve(__dirname, 'pages/permissions.html'),
        'downloads-dialog': resolve(__dirname, 'pages/downloads-dialog.html'),
        'add-bookmark': resolve(__dirname, 'pages/add-bookmark.html'),
        'show-block': resolve(__dirname, 'pages/show-block.html'),
        'show-video': resolve(__dirname, 'pages/show-video.html'),
        'network-error': resolve(__dirname, 'pages/network-error.html'),

      },
      output: {
        // 确保HTML文件输出到根目录
        entryFileNames: (chunkInfo) => {
          // HTML入口文件的JS输出到assets目录
          return 'assets/[name]-[hash].js';
        },
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      },
      external: [
        'electron',
        'path',
        'fs',
        'os',
        'crypto',
        'buffer',
        'stream',
        'util',
        'events',
        'url',
        'querystring',
        'http',
        'https',
        'net',
        'tls',
        'zlib',
        'child_process',
        '@electron/remote',
        '@mario-ai/shared'
      ],
    },
  },
  server: {
    port: 4444,
    host: true,
    // 配置开发服务器以支持多入口点
    middlewareMode: false,
    // 配置静态文件服务
    fs: {
      allow: ['..']
    }
  },
})