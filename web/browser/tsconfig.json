{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@root/*": ["../../*"], "@browser/*": ["./src/*"], "@browser/core/*": ["./src/core/*"], "@browser/core/components/*": ["./src/core/components/*"], "@browser/core/constants/*": ["./src/core/constants/*"], "@browser/core/styles/*": ["./src/core/styles/*"], "@browser/core/types/*": ["./src/core/types/*"], "@browser/core/utils/*": ["./src/core/utils/*"]}}, "include": ["./**/*.ts", "./**/*.tsx"], "references": [{"path": "./tsconfig.node.json"}]}