import * as React from 'react';
import { observer } from 'mobx-react-lite';

import { cn } from '@browser/utils/tailwind-helpers';
import store from '../../store';
import { Input, Dropdown } from '@browser/core/components/Input';
import { Button } from '@browser/core/components/Button';
import { eventUtils, menuUtils } from '@browser/core/utils/platform-lite';
import { getBookmarkTitle } from '@browser/views/bookmarks/utils';
import { UIStyle } from '@browser/core/styles/default-styles';

const updateBookmark = () => {
  if (!store.bookmark) return;
  console.log('[AddBookmark] Updating bookmark:', store.bookmark);
  eventUtils.send('bookmarks-update', store.bookmark._id, store.bookmark);
};

const onDone = () => {
  console.log('[AddBookmark] Done button clicked');
  // 通知主进程更新书签状态
  eventUtils.send('update-bookmark-status');
  store.hide();
};



const onChange = () => {
  console.log('[AddBookmark] Input changed');
  if (store.bookmark && store.titleRef.current) {
    store.bookmark.title = store.titleRef.current.value;
    updateBookmark();
  }
};

const onDropdownClick = (e: React.MouseEvent<HTMLDivElement>) => {
  console.log('[AddBookmark] Dropdown clicked');
  const { left, top, height } = e.currentTarget.getBoundingClientRect();

  console.log('[AddBookmark] Available folders:', store.folders.length);
  const menuItems = store.folders.map((folder) => ({
    label: getBookmarkTitle(folder),
    click: () => {
      console.log('[AddBookmark] Folder selected:', folder);
      store.currentFolder = folder;
      if (store.bookmark) {
        store.bookmark.parent = folder._id;
        updateBookmark();
      }
    },
  }));

  console.log('[AddBookmark] Showing context menu with', menuItems.length, 'items');
  menuUtils.showContextMenu(menuItems, { x: left, y: top + height });
};

const onRemove = () => {
  console.log('[AddBookmark] Remove button clicked');
  if (!store.bookmark) {
    console.warn('[AddBookmark] No bookmark to remove');
    return;
  }
  console.log('[AddBookmark] Removing bookmark:', store.bookmark._id);
  eventUtils.send('bookmarks-remove', [store.bookmark._id]);
  // 通知主进程更新书签状态
  eventUtils.send('update-bookmark-status');
  store.hide();
};

export const App = observer(() => {
  console.log('[AddBookmark] Rendering App, visible:', store.visible);

  // StyledApp 样式 - Tailwind 版本 (继承 DialogStyle)
  const appClasses = cn(
    'p-4 rounded-[10px] shadow-dialog bg-mario-dialog',
    'animate-[fadeIn_0.15s_ease-out]',
    // textfield 和 dropdown 样式
    '[&_.textfield]:w-[255px] [&_.textfield]:ml-auto',
    '[&_.dropdown]:w-[255px] [&_.dropdown]:ml-auto',
    // 文本颜色根据主题
    store.theme['dialog.lightForeground'] ? 'text-white' : 'text-black'
  );

  // Title 样式
  const titleClasses = cn(
    'text-base mb-4 font-roboto font-normal' // font-size: 16px, margin-bottom: 16px
  );

  // Row 样式
  const rowClasses = cn(
    'w-full h-12 items-center flex' // height: 48px
  );

  // Label 样式
  const labelClasses = cn(
    'text-xs' // font-size: 12px
  );

  // Buttons 样式
  const buttonsClasses = cn(
    'w-full flex items-center justify-end mt-4',
    // 子元素 button 的右边距 (除了最后一个)
    '[&_.button:not(:last-child)]:mr-2'
  );

  return (
    <div className={appClasses}>
      <UIStyle />
      <div className={titleClasses}>{store.dialogTitle}</div>
      <div className={rowClasses}>
        <div className={labelClasses}>名称</div>
        <Input
          tabIndex={0}
          className="textfield"
          ref={store.titleRef}
          onChange={onChange}
        />
      </div>
      <div className={rowClasses}>
        <div className={labelClasses}>文件夹</div>
        <Dropdown
          dark={store.theme['dialog.lightForeground']}
          tabIndex={1}
          className="dropdown"
          onMouseDown={onDropdownClick}
        >
          {store.currentFolder && getBookmarkTitle(store.currentFolder)}
        </Dropdown>
      </div>
      <div className={buttonsClasses}>
        <Button onClick={onDone}>完成</Button>
        <Button
          onClick={onRemove}
          background={
            store.theme['dialog.lightForeground']
              ? 'rgba(255, 255, 255, 0.08)'
              : 'rgba(0, 0, 0, 0.08)'
          }
          foreground={
            store.theme['dialog.lightForeground'] ? 'white' : 'black'
          }
        >
          删除
        </Button>
      </div>
    </div>
  );
});
