import * as React from 'react';
import { observer } from 'mobx-react-lite';

import store from '../../store';
import { IFormFillData } from '@mario-ai/shared';
import { cn } from '@browser/utils/tailwind-helpers';
import { transparency, ICON_CLOSE } from '@mario-ai/shared';

const onDelete = (data: IFormFillData) => () => {
  store.remove(data);
};

const Item = ({ data }: { data: IFormFillData }) => {
  const { username, passLength } = data.fields;

  // StyledItem 样式 - Tailwind 版本
  const itemClasses = cn('flex h-8 items-center'); // height: 32px

  // Username 样式
  const usernameClasses = cn(
    'text-sm flex-[2]', // font-size: 13px, flex: 2
    'font-roboto font-normal' // robotoRegular mixin
  );

  const usernameStyle: React.CSSProperties = {
    color: `rgba(0, 0, 0, ${transparency.text.medium})`,
  };

  // Password 样式 (继承 Username 样式)
  const passwordClasses = cn(
    'text-sm flex-1', // font-size: 13px, flex: 1
    'font-roboto font-normal'
  );

  // DeleteIcon 样式
  const deleteIconClasses = cn(
    'w-4 h-4 cursor-pointer bg-center bg-contain bg-no-repeat',
    'hover:opacity-100'
  );

  const deleteIconStyle: React.CSSProperties = {
    backgroundImage: `url(${ICON_CLOSE})`,
    opacity: transparency.icons.inactive,
  };

  return (
    <div className={itemClasses}>
      <div className={usernameClasses} style={usernameStyle}>
        {username}
      </div>
      <div className={passwordClasses} style={usernameStyle}>
        {'•'.repeat(passLength)}
      </div>
      <div
        className={deleteIconClasses}
        style={deleteIconStyle}
        onClick={onDelete(data)}
      />
    </div>
  );
};

export default observer(() => {
  return (
    <div style={{ display: store.content === 'list' ? 'block' : 'none' }}>
      {store.list.map((data) => (
        <Item key={data._id} data={data} />
      ))}
    </div>
  );
});
