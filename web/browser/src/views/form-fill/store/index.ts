import { eventUtils } from '@browser/core/utils/platform-lite';
import { makeObservable, observable } from 'mobx';

import { IFormFillMenuItem } from '@mario-ai/shared';
import { LiteDialogStore } from '@browser/core/utils/platform-lite';

export class Store extends LiteDialogStore {
  public items: IFormFillMenuItem[] = [];

  public constructor() {
    super({ hideOnBlur: false });

    makeObservable({ items: observable });

    eventUtils.on(`formfill-get-items`, (e, items) => {
      this.items = items;
    });
  }
}

export default new Store();
