import * as React from 'react';
import { observer } from 'mobx-react-lite';

import { IBookmark } from '@mario-ai/shared';
import store from '../../store';
import { getBookmarkTitle } from '../../utils';
import { cn } from '@browser/utils/tailwind-helpers';
import { transparency, ICON_FOLDER, ICON_DROPDOWN } from '@mario-ai/shared';

const onClick = (item: IBookmark) => () => {
  store.currentFolder = item._id;
};

const onDropClick = (item: IBookmark) => (
  e: React.MouseEvent<HTMLDivElement>,
) => {
  e.stopPropagation();

  if (item.children.length > 0) {
    item.expanded = !item.expanded;
  }
};

const TreeItem = observer(
  ({ depth, data }: { depth: number; data: IBookmark }) => {
    if (!data) return null;

    const children = data.children || [];

    const c = children
      .map((x) => store.list.find((y) => x === y._id))
      .filter((x) => x && x.isFolder);

    const selected = store.currentFolder === data._id;
    const hasChildren = c.length !== 0;

    // StyledTreeItem 样式 - Tailwind 版本
    const treeItemClasses = cn(
      'w-full h-8 flex mt-1 items-center cursor-pointer rounded',
      // 背景色根据选中状态和主题
      selected
        ? store.theme['pages.lightForeground']
          ? 'bg-white/6'
          : 'bg-black/6'
        : 'bg-transparent',
      // hover 效果
      !selected && (store.theme['pages.lightForeground']
        ? 'hover:bg-white/4'
        : 'hover:bg-black/4')
    );

    // DropIcon 样式 - Tailwind 版本
    const dropIconClasses = cn(
      'min-w-6 min-h-6 mx-0.5 rounded-full bg-center bg-no-repeat bg-contain',
      'transition-all duration-200',
      // 可见性和旋转
      hasChildren ? `opacity-[${transparency.icons.inactive}]` : 'opacity-0',
      data.expanded ? '-rotate-90' : 'rotate-0',
      // 过滤器根据主题
      store.theme['pages.lightForeground'] ? 'invert' : '',
      // hover 效果
      'hover:bg-black/10',
      hasChildren && 'hover:opacity-100'
    );

    const dropIconStyle = {
      backgroundImage: `url(${ICON_DROPDOWN})`,
      backgroundSize: '20px'
    };

    // FolderIcon 样式 - Tailwind 版本
    const folderIconClasses = cn(
      'min-w-6 min-h-6 bg-center bg-no-repeat bg-contain',
      `opacity-[${transparency.icons.inactive}]`,
      // 过滤器根据主题
      store.theme['pages.lightForeground'] ? 'invert' : ''
    );

    const folderIconStyle = {
      backgroundImage: `url(${ICON_FOLDER})`,
      backgroundSize: '20px'
    };

    // Label 样式 - Tailwind 版本
    const labelClasses = cn(
      'text-[13px] ml-3 overflow-hidden text-ellipsis whitespace-nowrap'
    );

    return (
      <>
        <div
          className={treeItemClasses}
          onClick={onClick(data)}
          style={{ paddingLeft: depth * 30 }}
        >
          <div
            className={dropIconClasses}
            style={dropIconStyle}
            onClick={onDropClick(data)}
          />
          <div className={folderIconClasses} style={folderIconStyle} />
          <div className={labelClasses}>{getBookmarkTitle(data)}</div>
        </div>
        {data.expanded &&
          c.map((item) => (
            <TreeItem key={item._id} data={item} depth={depth + 1} />
          ))}
      </>
    );
  },
);

export default TreeItem;
