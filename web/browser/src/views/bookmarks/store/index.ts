import { observable, computed, action, toJS, makeObservable } from 'mobx';
import { ISettings, IFavicon, ITheme, IBookmark } from '@mario-ai/shared';
import { getTheme } from '@browser/core/utils/themes';
import { eventUtils } from '@browser/core/utils/platform-lite';
import * as React from 'react';
import { Textfield } from '@browser/core/components/Textfield';

export class Store {
  // ✅ 重构: 移除复杂的数据库抽象层，改为直接IPC调用 (借鉴原始工程)

  public nameInputRef = React.createRef<Textfield>();

  public urlInputRef = React.createRef<Textfield>();

  @observable
  public settings: ISettings = { ...(window as any).settings };

  @observable
  public list: IBookmark[] = [];

  @observable
  public itemsLoaded = this.getDefaultLoaded();

  @observable
  public menuLeft = 0;

  @observable
  public menuTop = 0;

  @observable
  public menuVisible = false;

  @observable
  public searched = '';

  @observable
  public searchValue = '';

  @observable
  public selectedItems: string[] = [];

  @observable
  public favicons: Map<string, string> = new Map();

  @observable
  public currentFolder: string = null;

  @observable
  private _dialogVisible = false;

  public showDialog(content: 'edit' | 'new-folder' | 'rename-folder') {
    this.dialogContent = content;
    this.dialogVisible = true;

    if (content === 'edit' || content === 'rename-folder') {
      // 使用 Textfield 组件的 value setter
      (this.nameInputRef.current as any).value = this.currentBookmark.title || '';

      if (content === 'edit') {
        (this.urlInputRef.current as any).value = this.currentBookmark.url || '';
      }
    }

    this.nameInputRef.current.inputRef.current.focus();
    this.nameInputRef.current.inputRef.current.select();
  }

  @observable
  public dialogContent: 'edit' | 'new-folder' | 'rename-folder' = 'new-folder';

  @observable
  public currentBookmark: IBookmark = null;

  // Computed

  @computed
  public get visibleItems() {
    return this.list
      .filter(
        (x) =>
          (this.searched !== '' &&
            ((x.url &&
              x.url.toLowerCase().includes(this.searched.toLowerCase())) ||
              (x.title &&
                x.title
                  .toLowerCase()
                  .includes(this.searched.toLowerCase())))) ||
          (this.searched === '' && x.parent === this.currentFolder),
      )
      .slice()
      .sort((a, b) => {
        return a.order - b.order;
      });
  }

  @computed
  public get dialogVisible() {
    return this._dialogVisible;
  }

  public set dialogVisible(value: boolean) {
    if (!value) {
      // 使用 Textfield 组件的 value setter 而不是直接设置 DOM 元素
      if (this.nameInputRef.current) {
        (this.nameInputRef.current as any).value = '';
      }
      if (this.urlInputRef.current) {
        (this.urlInputRef.current as any).value = '';
      }
    }

    this.menuVisible = false;

    this._dialogVisible = value;
  }

  @computed
  public get theme(): ITheme {
    return getTheme(this.settings.theme);
  }

  @computed
  public get path() {
    return this.getFolderPath(this.currentFolder);
  }

  @computed
  public get folders() {
    return this.list.filter((x) => x.isFolder);
  }

  public constructor() {
    makeObservable(this);

    (window as any).updateSettings = (settings: ISettings) => {
      this.settings = { ...this.settings, ...settings };
    };

    this.load();
    this.loadFavicons();

    window.addEventListener('resize', () => {
      const loaded = this.getDefaultLoaded();

      if (loaded > this.itemsLoaded) {
        this.itemsLoaded = loaded;
      }
    });

    window.addEventListener('mousedown', () => {
      this.menuVisible = false;
    });

    // 监听书签重新加载事件
    (window as any).reloadBookmarks = () => {
      console.log('[BookmarksStore] Received reload-bookmarks event');
      this.load();
      this.loadFavicons();
    };

    // 监听浏览数据清除事件，重新加载图标
    (window as any).onBrowsingDataCleared = () => {
      console.log('[BookmarksStore] Received browsing-data-cleared event');
      this.loadFavicons();
    };
  }

  public resetLoadedItems(): void {
    this.itemsLoaded = this.getDefaultLoaded();
  }

  public getById(id: string) {
    return this.list.find((x) => x._id === id);
  }

  public async load() {
    try {
      // 检查是否在真正的Electron环境中（有require函数）
      const isElectron = typeof window !== 'undefined' && (window as any).require;

      if (isElectron) {
        console.log('[BookmarksStore] Running in Electron environment, loading real bookmarks');
        const items: IBookmark[] = await eventUtils.invoke('bookmarks-get');
        this.list = items.map((x) => ({ ...x }));
        const mainFolder = this.list.find((x) => x.static === 'main');
        this.currentFolder = mainFolder ? mainFolder._id : null;
        console.log('[BookmarksStore] Real bookmarks loaded:', this.list.length);
      } else {
        console.log('[BookmarksStore] Running in browser environment, using mock data');
        // 在浏览器环境中使用mock数据
        this.list = [
          {
            _id: 'main-folder-mock',
            title: 'Bookmarks bar',
            static: 'main',
            isFolder: true,
            children: [],
            order: 0
          },
          {
            _id: 'other-folder-mock',
            title: 'Other bookmarks',
            static: 'other',
            isFolder: true,
            children: [],
            order: 1
          }
        ];
        const mainFolder = this.list.find((x) => x.static === 'main');
        this.currentFolder = mainFolder ? mainFolder._id : null;
        console.log('[BookmarksStore] Mock bookmarks loaded:', this.list.length);
      }
    } catch (error) {
      console.error('[BookmarksStore] Error loading bookmarks:', error);
      this.list = [];
      this.currentFolder = null;
    }
  }

  public async loadFavicons() {
    try {
      // 检查是否在真正的Electron环境中
      const isElectron = typeof window !== 'undefined' && (window as any).require;

      if (isElectron) {
        console.log('[BookmarksStore] Loading favicons via direct IPC');
        // 使用直接IPC调用，避免复杂的数据库抽象层
        const favicons: IFavicon[] = await eventUtils.invoke('storage-get', {
          scope: 'favicons',
          query: {}
        });

        favicons.forEach((favicon) => {
          const { data } = favicon;
          if (this.favicons.get(favicon.url) == null) {
            this.favicons.set(favicon.url, data);
          }
        });
        console.log('[BookmarksStore] Favicons loaded via IPC:', favicons.length);
      } else {
        console.log('[BookmarksStore] Browser environment, skipping favicon loading');
      }
    } catch (error) {
      console.error('[BookmarksStore] Error loading favicons:', error);
    }
  }

  public removeItems(ids: string[]) {
    for (const id of ids) {
      const item = this.list.find((x) => x._id === id);
      const parent = this.list.find((x) => x._id === item.parent);
      parent.children = parent.children.filter((x) => x !== id);
    }
    this.list = this.list.filter((x) => !ids.includes(x._id));

    // ✅ 重构: eventUtils.send会自动序列化，无需手动toJS (统一序列化策略)
    eventUtils.send('bookmarks-remove', ids);
  }

  public async addItem(item: IBookmark) {
    // ✅ 重构: eventUtils.invoke会自动序列化，无需手动toJS (统一序列化策略)
    const i = await eventUtils.invoke('bookmarks-add', item);
    this.list.push({ ...i });
    this.list.find((x) => x._id === i.parent).children.push(i._id);
    return i;
  }

  public async updateItem(id: string, change: IBookmark) {
    const index = this.list.indexOf(this.list.find((x) => x._id === id));
    this.list[index] = { ...this.list[index], ...change };
    // ✅ 重构: eventUtils.send会自动序列化，无需手动toJS (统一序列化策略)
    eventUtils.send('bookmarks-update', id, change);
  }

  @action
  public search(str: string) {
    this.searchValue = str;
    this.searched = str.toLowerCase().toLowerCase();
    this.itemsLoaded = this.getDefaultLoaded();
  }

  public getDefaultLoaded() {
    return Math.floor(window.innerHeight / 48);
  }

  @action
  public deleteSelected() {
    this.removeItems(this.selectedItems);
    this.selectedItems = [];
  }

  private getFolderPath(parent: string) {
    const parentFolder = this.list.find((x) => x._id === parent);
    let path: IBookmark[] = [];

    if (parentFolder == null) return [];

    if (parentFolder.parent != null) {
      path = path.concat(this.getFolderPath(parentFolder.parent));
    }

    path.push(parentFolder);
    return path;
  }
}

export default new Store();
