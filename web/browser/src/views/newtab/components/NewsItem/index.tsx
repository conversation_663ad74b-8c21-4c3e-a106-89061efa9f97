import * as React from 'react';
import { observer } from 'mobx-react-lite';

import { INewsItem } from '@mario-ai/shared';
import { cn } from '@browser/utils/tailwind-helpers';

export const NewsItem = observer(({ item }: { item: INewsItem }) => {
  // eslint-disable-next-line prefer-const
  let [img, setImg] = React.useState('');

  const image = new Image();
  const src = item.urlToImage;

  image.onload = () => {
    setImg(src);
  };
  image.src = src;

  if (image.complete) {
    img = src;
  }

  // StyledNewsItem 样式 - Tailwind 版本
  const newsItemClasses = cn(
    'rounded-md overflow-hidden relative text-white no-underline cursor-pointer',
    'animate-[fadein_0.3s] will-change-[opacity]',
    // 复杂的网格布局 - 每16个项目的第1个占2列
    '[&:nth-child(16n-15)]:col-span-2',
    // 伪元素效果 - 渐变遮罩
    '[&:nth-child(16n-15)]:after:absolute [&:nth-child(16n-15)]:after:inset-0',
    '[&:nth-child(16n-15)]:after:bg-gradient-to-l [&:nth-child(16n-15)]:after:from-transparent [&:nth-child(16n-15)]:after:to-black',
    '[&:nth-child(16n-15)]:after:opacity-75 [&:nth-child(16n-15)]:after:pointer-events-none',
    // 大项目的特殊样式
    '[&:nth-child(16n-15)] [&>div:first-child]:flex [&:nth-child(16n-15)] [&>div:first-child]:flex-col',
    '[&:nth-child(16n-15)] [&>div:first-child]:h-auto [&:nth-child(16n-15)] [&>div:first-child]:min-h-[200px]',
    '[&:nth-child(16n-15)] [&>div:first-child]:p-6',
    // 描述文本在大项目中显示
    '[&:nth-child(16n-15)] [&_div:nth-child(2)]:block',
    // Fill 在大项目中显示
    '[&:nth-child(16n-15)] [&_div:nth-child(3)]:block',
    // hover 效果
    'hover:shadow-lg transition-shadow duration-200'
  );

  // Img 样式 - Tailwind 版本
  const imgClasses = cn(
    'transition-all duration-500 relative overflow-hidden bg-cover bg-center h-full',
    'will-change-[opacity]',
    // 透明度根据图片加载状态
    img === '' ? 'opacity-0' : 'opacity-100'
  );

  const imgStyle = {
    backgroundImage: img ? `url(${img})` : 'none'
  };

  // Info 样式 - Tailwind 版本
  const infoClasses = cn(
    'absolute bottom-0 left-0 right-0 p-4 z-[2] flex flex-col h-auto min-h-[100px]',
    // 背景渐变
    'bg-gradient-to-t from-black/80 to-transparent'
  );

  // Title 样式 - Tailwind 版本
  const titleClasses = cn(
    'font-medium text-base leading-5 mb-0',
    // 文本截断 - 最多3行
    'overflow-hidden',
    'line-clamp-3'
  );

  // Description 样式 - Tailwind 版本
  const descriptionClasses = cn(
    'overflow-hidden mt-2 leading-6 relative opacity-80',
    // 默认隐藏，在大项目中显示
    'hidden',
    // 文本截断 - 最多3行
    'line-clamp-3'
  );

  // Fill 样式 - Tailwind 版本
  const fillClasses = cn(
    'flex-[2] hidden'
  );

  // Footer 样式 - Tailwind 版本
  const footerClasses = cn(
    'flex items-center mt-2'
  );

  // Source 样式 - Tailwind 版本
  const sourceClasses = cn(
    'text-xs opacity-80 uppercase tracking-wider font-medium'
  );

  return (
    <a className={newsItemClasses} href={item.url}>
      <div className={imgClasses} style={imgStyle}></div>
      <div className={infoClasses}>
        <div className={titleClasses}>{item.title}</div>
        <div className={descriptionClasses}>{item.description}</div>
        <div className={fillClasses} />
        <div className={footerClasses}>
          <div className={sourceClasses}>{item.source.name}</div>
        </div>
      </div>
    </a>
  );
});
