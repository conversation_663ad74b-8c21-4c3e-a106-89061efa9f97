import * as React from 'react';
import { observer } from 'mobx-react-lite';

import store from '../../store';
import { TopSite } from '../TopSite';
import { cn } from '@browser/utils/tailwind-helpers';

export const TopSites = observer(() => {
  // StyledTopSites 样式 - 完全对应原始样式
  const topSitesClasses = cn(
    'grid',
    // 原始：grid-auto-flow: row
    'grid-flow-row',
    // 原始：justify-content: center  
    'justify-center',
    // 原始：grid-template-columns: repeat(8, auto)
    'grid-cols-[repeat(8,auto)]',
    // 响应式：@media only screen and (max-width: 1200px) { grid-template-columns: repeat(4, auto) }
    'max-[1200px]:grid-cols-[repeat(4,auto)]',
    // 原始：grid-gap: 6px
    'gap-1.5'
  );

  const topSitesStyle = {
    // 原始样式完全对应
    marginTop: '128px',
    marginBottom: '128px'
  };

  // Placeholder 样式 - Tailwind 版本
  const placeholderClasses = cn(
    'rounded-md w-[120px] h-[90px] box-border border-2 border-dashed',
    // 边框颜色根据主题和图片状态
    !store.imageVisible || !store.image
      ? store.theme['pages.lightForeground']
        ? 'border-white/30'
        : 'border-black/20'
      : 'border-white/30'
  );

  return (
    <div className={topSitesClasses} style={topSitesStyle}>
      {store.topSites.map((item) => (
        <TopSite key={item._id} item={item} />
      ))}
      {store.topSites.length < 8 && <TopSite item={{}}/>}
      {Array(8 - Math.min(8, store.topSites.length + 1))
        .fill(1)
        .map((v, i) => (
          <div className={placeholderClasses} key={i} />
        ))}
    </div>
  );
});
