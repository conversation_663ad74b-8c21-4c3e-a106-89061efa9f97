import { eventUtils, shellUtils } from '@browser/core/utils/platform-lite';
import { action, makeObservable, observable } from 'mobx';
import { IDownloadItem } from '@mario-ai/shared';
import { LiteDialogStore } from '@browser/core/utils/platform-lite';

export class Store extends LiteDialogStore {
  public downloads: IDownloadItem[] = [];

  public maxHeight = 0;

  public constructor() {
    super();

    makeObservable(this, { downloads: observable, maxHeight: observable });

    this.init();

    eventUtils.on('download-started', (e, item) => {
      this.downloads.push(item);
    });

    eventUtils.on('download-progress', (e, item: IDownloadItem) => {
      const index = this.downloads.indexOf(
        this.downloads.find((x) => x.id === item.id),
      );

      this.downloads[index] = {
        ...this.downloads[index],
        ...item,
      };
    });

    eventUtils.on('download-completed', (e, id: string) => {
      const i = this.downloads.find((x) => x.id === id);
      i.completed = true;
      if (i.openWhenDone) {
        shellUtils.openPath(i.savePath);
      }
    });

    eventUtils.on('download-paused', (e, id: string) => {
      const i = this.downloads.find((x) => x.id === id);
      i.paused = true;
    });

    eventUtils.on('download-canceled', (e, id: string) => {
      const i = this.downloads.find((x) => x.id === id);
      i.completed = false;
      i.canceled = true;
    });

    eventUtils.on('download-removed', (e, id: string) => {
      this.downloads = this.downloads.filter((x) => x.id !== id);
    });

    eventUtils.on(
      'download-open-when-done-change',
      (e, item: IDownloadItem) => {
        const index = this.downloads.indexOf(
          this.downloads.find((x) => x.id === item.id),
        );
        this.downloads[index].openWhenDone = item.openWhenDone;
      },
    );

    eventUtils.on('max-height', (e, height) => {
      this.maxHeight = height;
    });
  }

  public async init() {
    this.downloads = await eventUtils.invoke('get-downloads');
  }

  @action
  public openMenu(item: IDownloadItem) {
    const state = item.menuIsOpen;
    this.closeAllDownloadMenu();
    const index = this.downloads.indexOf(
      this.downloads.find((x) => x.id === item.id),
    );

    this.downloads[index] = {
      ...this.downloads[index],
      menuIsOpen: !state,
    };
  }

  @action
  public closeAllDownloadMenu() {
    const downloads = this.downloads.map((download) => ({
      ...download,
      menuIsOpen: false,
    }));
    this.downloads = downloads;
  }
}

export default new Store();
