import * as React from 'react';
import { observer } from 'mobx-react-lite';
import { IDownloadItem } from '@mario-ai/shared';
import prettyBytes from 'pretty-bytes';
import { shellUtils } from '@browser/core/utils/platform-lite';
import store from '../../store';
import { DownloadItemMenu } from '../DownloadItemMenu';
import { cn } from '@browser/utils/tailwind-helpers';
import { BLUE_500, ICON_PAGE, ICON_MORE } from '@mario-ai/shared';

const onClick = (item: IDownloadItem) => () => {
  if (item.completed) {
    shellUtils.openPath(item.savePath);
  }
};

const onMoreClick =
  (item: IDownloadItem) => (e: React.MouseEvent<HTMLDivElement>) => {
    store.openMenu(item);
    e.stopPropagation();
  };

export const DownloadItem = observer(({ item }: { item: IDownloadItem }) => {
  let received = prettyBytes(item.receivedBytes);
  const total = prettyBytes(item.totalBytes);

  const receivedSplit = received.split(' ');

  if (receivedSplit[1] === total.split(' ')[1]) {
    received = receivedSplit[0];
  }

  // StyledDownloadItem 样式 - Tailwind 版本
  const downloadItemClasses = cn(
    'h-16 rounded-lg flex items-center relative mt-2 transition-colors duration-100',
    'first:mt-0',
    // 背景色根据主题
    store.theme['dialog.lightForeground']
      ? 'bg-white/4 hover:bg-white/6'
      : 'bg-black/2 hover:bg-black/4'
  );

  // Icon 样式 - Tailwind 版本
  const iconClasses = cn(
    'w-6 h-6 bg-center bg-no-repeat bg-contain opacity-54 mr-4 ml-4',
    // 过滤器根据主题
    store.theme['dialog.lightForeground'] ? 'invert' : ''
  );

  const iconStyle = {
    backgroundImage: `url(${ICON_PAGE})`
  };

  // Info 样式 - Tailwind 版本
  const infoClasses = cn(
    'flex flex-col justify-center flex-1 overflow-hidden text-ellipsis whitespace-nowrap'
  );

  // Title 样式 - Tailwind 版本
  const titleClasses = cn(
    'overflow-hidden text-ellipsis',
    // 删除线根据取消状态
    item.canceled ? 'line-through' : ''
  );

  // SecondaryText 样式 - Tailwind 版本
  const secondaryTextClasses = cn(
    'opacity-54 text-[11px] mt-1'
  );

  // ProgressBackground 样式 - Tailwind 版本
  const progressBgClasses = cn(
    'h-[5px] rounded-2xl overflow-hidden mt-1 flex-1',
    // 背景色根据主题
    store.theme['dialog.lightForeground']
      ? 'bg-white/12'
      : 'bg-black/12'
  );

  // Progress 样式 - Tailwind 版本
  const progressClasses = cn(
    'h-[5px] rounded-2xl',
    `bg-[${BLUE_500}]`
  );

  // Separator 样式 - Tailwind 版本
  const separatorClasses = cn(
    'h-12 w-px ml-4 mr-2',
    // 背景色根据主题
    store.theme['dialog.lightForeground']
      ? 'bg-white/12'
      : 'bg-black/12'
  );

  // MoreButton 样式 - Tailwind 版本
  const moreButtonClasses = cn(
    'w-9 h-9 bg-center bg-no-repeat bg-contain opacity-54 mr-2 rounded-md',
    'transition-colors duration-100 hover:bg-black/6',
    // 过滤器根据主题
    store.theme['dialog.lightForeground'] ? 'invert' : '',
    // 背景色根据切换状态
    item.menuIsOpen ? 'bg-black/8' : 'bg-transparent'
  );

  const moreButtonStyle = {
    backgroundImage: `url(${ICON_MORE})`,
    backgroundSize: '20px'
  };

  return (
    <div className={downloadItemClasses} onClick={onClick(item)}>
      <div className={iconClasses} style={iconStyle}></div>
      <div className={infoClasses}>
        <div className={titleClasses}>{item.fileName}</div>
        {!item.completed && !item.canceled && (
          <>
            <div className={progressBgClasses}>
              <div
                className={progressClasses}
                style={{
                  width: `calc((${item.receivedBytes} / ${item.totalBytes}) * 100%)`,
                }}
              ></div>
            </div>
            <div className={secondaryTextClasses}>{`${received}/${total} ${
              item.paused ? ', Paused' : ''
            }`}</div>
          </>
        )}
        {item.canceled && <div className={secondaryTextClasses}>Canceled</div>}
      </div>
      <div className={separatorClasses}></div>
      <div
        className={moreButtonClasses}
        style={moreButtonStyle}
        onClick={onMoreClick(item)}
      ></div>
      <DownloadItemMenu item={item} visible={item.menuIsOpen} />
    </div>
  );
});
