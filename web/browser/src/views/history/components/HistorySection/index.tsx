import * as React from 'react';

import HistoryItem from '../HistoryItem';
import { IHistorySection } from '@mario-ai/shared';
import { observer } from 'mobx-react-lite';
import { cn } from '@browser/utils/tailwind-helpers';
import store from '../../store';

export const HistorySection = observer(
  ({ data }: { data: IHistorySection }) => {
    // EmptySection 样式 - Tailwind 版本
    const emptySectionClasses = cn(
      'mt-4 pb-2 overflow-hidden rounded-lg',
      'first:mt-0',
      // 背景色根据主题
      store.theme['pages.lightForeground']
        ? 'bg-white/5'
        : 'bg-[#fafafa]'
    );

    // SectionTitle 样式 - Tailwind 版本
    const sectionTitleClasses = cn(
      'text-base px-6 py-4 font-medium'
    );

    return (
      <div className={emptySectionClasses}>
        <div className={sectionTitleClasses}>{data.label}</div>
        {data.items.map((item) => (
          <HistoryItem key={item._id} data={item} />
        ))}
      </div>
    );
  },
);
