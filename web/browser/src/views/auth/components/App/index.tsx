import * as React from 'react';
import { observer } from 'mobx-react-lite';

import { But<PERSON> } from '@browser/core/components/Button';
import store from '../../store';
import { Textfield } from '@browser/core/components/Textfield';
import { PasswordInput } from '@browser/core/components/PasswordInput';
import { UIStyle } from '@browser/core/styles/default-styles';
import { cn } from '@browser/utils/tailwind-helpers';

const ref1 = React.createRef<Textfield>();
const ref2 = React.createRef<PasswordInput>();

const sendResponse = (credentials: any) => {
  store.send('result', credentials);
};

const onClick = () => {
  if (ref1.current.test() && ref2.current.test()) {
    sendResponse({
      username: ref1.current.value,
      password: ref2.current.value,
    });
  }
};

export const App = observer(() => {
  // StyledApp 样式 - Tailwind 版本
  const appClasses = cn(
    'm-4 p-4 rounded-[10px] shadow-dialog overflow-hidden relative',
    'bg-mario-dialog',
    store.theme['dialog.lightForeground'] ? 'text-white' : 'text-black'
  );

  // Title 样式
  const titleClasses = cn('text-base'); // font-size: 16px

  // Subtitle 样式
  const subtitleClasses = cn(
    'text-sm opacity-54 mt-2' // font-size: 13px, margin-top: 8px
  );

  // Buttons 样式
  const buttonsClasses = cn(
    'flex mt-6 float-right' // margin-top: 24px
  );

  return (
    <div className={appClasses}>
      <UIStyle />
      <div className={titleClasses}>Login</div>
      <div className={subtitleClasses}>{store.url}</div>
      <Textfield
        dark={store.theme['dialog.lightForeground']}
        ref={ref1}
        test={(str) => str.trim().length !== 0}
        style={{ width: '100%', marginTop: 16 }}
        label="Username"
      ></Textfield>
      <PasswordInput
        dark={store.theme['dialog.lightForeground']}
        ref={ref2}
        style={{ width: '100%', marginTop: 16 }}
      ></PasswordInput>
      <div className={buttonsClasses}>
        <Button onClick={onClick}>Login</Button>
        <Button
          background={
            store.theme['dialog.lightForeground']
              ? 'rgba(255, 255, 255, 0.08)'
              : 'rgba(0, 0, 0, 0.08)'
          }
          foreground={
            store.theme['dialog.lightForeground'] ? 'white' : 'black'
          }
          style={{ marginLeft: 8 }}
          onClick={() => sendResponse(null)}
        >
          Cancel
        </Button>
      </div>
      <div style={{ clear: 'both' }}></div>
    </div>
  );
});
