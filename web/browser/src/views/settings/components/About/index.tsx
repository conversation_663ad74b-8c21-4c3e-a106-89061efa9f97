import * as React from 'react';

import {Header, Row, Title, Control, SecondaryText} from '../shared-styles';
import {NormalButton} from "@browser/views/settings/components/App";
import store from "@browser/views/settings/store";
const goUrl = (url: string) => () => {
  window.location.href = url;
};

export const About = () => {
  let chromeVersion = navigator.userAgent;
  try {
    const chromeVersionRegex = /Chrome\/([\d\.]+)/;
    const matches = navigator.userAgent.match(chromeVersionRegex);
    if (matches) {
      chromeVersion = matches[1];
    }
  } catch (e) {
    console.log(e);
  }
  return (
    <>
      <Header>关于</Header>
      <Row theme={store.theme}>
        <div>
          <Title>当前版本：{store.appVersion}</Title>
          <SecondaryText>Chromium {chromeVersion}</SecondaryText>
        </div>
      </Row>
    </>
  );
};
