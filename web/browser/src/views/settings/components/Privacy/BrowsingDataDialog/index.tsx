import * as React from 'react';
import { observer } from 'mobx-react-lite';

import { cn } from '@browser/utils/tailwind-helpers';
import { Button } from '@browser/core/components/Button';
import { Switch } from '@browser/core/components/Switch';
import store from '../../../store';
import { eventUtils } from '@browser/core/utils/platform-lite';

const clearData = async () => {
  console.log('[BrowsingDataDialog] Starting to clear browsing data...');

  try {
    // 发送清除浏览数据事件到主进程
    console.log('[BrowsingDataDialog] Sending clear-browsing-data event...');
    eventUtils.send('clear-browsing-data');

    // 也尝试调用invoke方式，确保事件被处理
    try {
      console.log('[BrowsingDataDialog] Invoking clear-browsing-data...');
      const result = await eventUtils.invoke('clear-browsing-data');
      console.log('[BrowsingDataDialog] Clear result:', result);
    } catch (error) {
      console.log('[BrowsingDataDialog] Invoke method failed, using send only:', error);
    }

    // 关闭对话框
    console.log('[BrowsingDataDialog] Closing dialog...');
    store.setDialogContent(null);

    // 显示成功消息
    console.log('[BrowsingDataDialog] Browsing data cleared successfully');
  } catch (error) {
    console.error('[BrowsingDataDialog] Error clearing browsing data:', error);
    // 即使出错也关闭对话框
    store.setDialogContent(null);
  }
}

export default observer(() => {
  console.log('[BrowsingDataDialog] Rendering with dialogContent:', store.dialogContent);

  // 监听数据清除完成事件
  React.useEffect(() => {
    const handleDataCleared = () => {
      console.log('[BrowsingDataDialog] Received browsing-data-cleared event');
      // 可以在这里添加用户反馈，比如显示成功消息
    };

    eventUtils.on('browsing-data-cleared', handleDataCleared);

    return () => {
      // 清理事件监听器
      if (eventUtils.off) {
        eventUtils.off('browsing-data-cleared', handleDataCleared);
      }
    };
  }, []);

  // Dialog 样式 - Tailwind 版本
  const dialogClasses = cn(
    'fixed w-[344px] p-4 left-1/2 top-1/2 rounded-[10px] z-[999]',
    'shadow-[0_8px_16px_rgba(0,0,0,0.24)] transition-opacity duration-200',
    'transform -translate-x-1/2 -translate-y-1/2',
    'bg-mario-dialog',
    store.theme['dialog.lightForeground'] ? 'text-white' : 'text-black',
    store.dialogContent === 'privacy' ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'
  );

  // Content 样式 - Tailwind 版本
  const contentClasses = cn(
    'mb-4'
  );

  // Option 样式 - Tailwind 版本
  const optionClasses = cn(
    'flex items-center justify-between py-2'
  );

  const labelClasses = cn(
    'text-sm'
  );

  return (
    <div className={dialogClasses}>
      <div className="text-base mb-4">清除浏览数据</div>
      <div className={contentClasses}>
        <div className={optionClasses}>
          <div className={labelClasses}>浏览历史记录</div>
          <Switch dense value={true} />
        </div>
        <div className={optionClasses}>
          <div className={labelClasses}>Cookie 和其他网站数据</div>
          <Switch dense value={true} />
        </div>
        <div className={optionClasses}>
          <div className={labelClasses}>缓存的图片和文件</div>
          <Switch dense value={true} />
        </div>
        <div className={optionClasses}>
          <div className={labelClasses}>下载历史记录</div>
          <Switch dense value={true} />
        </div>
        <div className={optionClasses}>
          <div className={labelClasses}>自动填充表单数据</div>
          <Switch dense value={true} />
        </div>
      </div>
      <div className="float-right flex mt-6">
        <Button background="transparent" foreground="#3F51B5" onClick={() => store.setDialogContent(null)}>
          取消
        </Button>
        <Button background="transparent" foreground="#3F51B5" onClick={clearData}>
          立即清除
        </Button>
      </div>
      <div style={{ clear: 'both' }}></div>
    </div>
  );
});
