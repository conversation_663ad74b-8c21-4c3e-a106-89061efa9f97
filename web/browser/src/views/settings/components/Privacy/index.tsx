import * as React from 'react';

import {Header, Row, Title, Control, SecondaryText} from '../shared-styles';
import { Button } from '@browser/core/components/Button';
import store from '../../store';
import { BLUE_500 } from '@mario-ai/shared';
import { observer } from 'mobx-react-lite';
import { onSwitchChange } from '../../utils';
import { Switch } from '@browser/core/components/Switch';
import {NormalButton} from "@browser/views/settings/components/App";
import BrowsingDataDialog from "@browser/views/settings/components/Privacy/BrowsingDataDialog";

const onClearBrowsingData = () => {
  console.log('[Privacy] Setting dialogContent to privacy');
  store.setDialogContent('privacy');
  console.log('[Privacy] dialogContent is now:', store.dialogContent);
};

const showAdblockRule = (event: React.MouseEvent) => {
  // 获取按钮位置
  const button = event.currentTarget as HTMLElement;
  const rect = button.getBoundingClientRect();

  console.log('[Privacy] Button position:', { x: rect.right, y: rect.bottom });

  window.postMessage(
    {
      type: 'show-adblock-rule',
      buttonPosition: {
        x: rect.right,
        y: rect.bottom
      }
    }, "*");
};

const DoNotTrackToggle = observer(() => {
  const { doNotTrack } = store.settings;

  return (
    <Row theme={store.theme} onClick={onSwitchChange('doNotTrack')}>
      <Title>
        Send a &quot;Do Not Track&quot; request with your browsing traffic
      </Title>
      <Control>
        <Switch value={doNotTrack} />
      </Control>
    </Row>
  );
});

export const Privacy = () => {
  return (
    <>
      <Header>拦截保护</Header>
      <Row theme={store.theme}>
        <div>
          <Title>广告拦截订阅</Title>
          <SecondaryText>支持订阅 ublock、adblockplus、adguard 格式地址</SecondaryText>
        </div>
        <Control>
          <NormalButton onClick={showAdblockRule}>修改</NormalButton>
        </Control>
      </Row>

      <Row theme={store.theme}>
        <div>
          <Title>浏览数据</Title>
        </div>
        <Control>
          <NormalButton onClick={onClearBrowsingData}>清除</NormalButton>
        </Control>
      </Row>
      <BrowsingDataDialog/>
      <DoNotTrackToggle />
    </>
  );
};
