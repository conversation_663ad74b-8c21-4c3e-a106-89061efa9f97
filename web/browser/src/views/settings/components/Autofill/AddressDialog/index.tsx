import * as React from 'react';
import { observer } from 'mobx-react-lite';

import { Button } from '@browser/core/components/Button';
import store from '../../../store';
import { Textfield } from '@browser/core/components/Textfield';
import { Dropdown } from '@browser/core/components/Dropdown';
import { Row } from '../../shared-styles';
import { cn } from '@browser/utils/tailwind-helpers';

export default observer(() => {
  const visible = store.dialogContent === 'edit-address';

  // Dialog 样式 - Tailwind 版本
  const dialogClasses = cn(
    'fixed w-[344px] p-4 left-1/2 top-1/2 rounded-[10px] z-[999]',
    'shadow-[0_8px_16px_rgba(0,0,0,0.24)] transition-opacity duration-200',
    'transform -translate-x-1/2 -translate-y-1/2',
    'bg-mario-dialog',
    store.theme['dialog.lightForeground'] ? 'text-white' : 'text-black',
    visible ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'
  );

  // Title 样式 - Tailwind 版本
  const titleClasses = cn(
    'text-base mb-4'
  );

  // Content 样式 - Tailwind 版本
  const contentClasses = cn(
    'mb-4'
  );

  // Buttons 样式 - Tailwind 版本
  const buttonsClasses = cn(
    'float-right flex mt-6'
  );

  if (!visible) return null;

  return (
    <div className={dialogClasses}>
      <div className={titleClasses}>Edit address</div>
      <div className={contentClasses}>
        <Textfield label="名称" />
        <Textfield label="街道地址" />
        <Row>
          <Textfield label="邮政编码" style={{ marginRight: 24 }} />
          <Textfield label="城市 " />
        </Row>
        <Dropdown>
          <Dropdown.Item value="pl">Poland</Dropdown.Item>
        </Dropdown>
      </div>
      <div className={buttonsClasses}>
        <Button
          background="transparent"
          foreground="#3F51B5"
          onClick={() => store.dialogContent = null}
        >
          取消
        </Button>
        <Button
          background="transparent"
          foreground="#3F51B5"
          style={{ marginLeft: 8 }}
        >
          保存
        </Button>
      </div>
      <div style={{ clear: 'both' }}></div>
    </div>
  );
});
