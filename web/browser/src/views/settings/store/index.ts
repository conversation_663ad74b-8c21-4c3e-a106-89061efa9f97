import { observable, computed, makeObservable, action } from 'mobx';
import * as React from 'react';
import { ISettings, ITheme, ISearchEngine } from '@mario-ai/shared';
import { AutoFillStore } from './autofill';
import { StartupTabsStore } from './startup-tabs';
import { getTheme } from '@browser/core/utils/themes';
import { Textfield } from '@browser/core/components/Textfield';
import { TailwindThemeManager } from '@browser/core/utils/tailwind-theme-manager';
import { eventUtils } from '@browser/core/utils/platform-lite';

// 默认设置
const DEFAULT_SETTINGS: Partial<ISettings> = {
  startupBehavior: { type: 'empty' },
  theme: 'wexond-light',
  searchEngines: [],
  searchEngine: 0,
};

export type SettingsSection =
  | 'appearance'
  | 'autofill'
  | 'address-bar'
  | 'privacy'
  | 'permissions'
  | 'startup'
  | 'language'
  | 'shortcuts'
  | 'downloads'
  | 'system'
  | 'search-engines'
  | 'extensions'
  | 'about';

export class Store {
  public autoFill = new AutoFillStore();
  public startupTabs = new StartupTabsStore();

  public menuRef = React.createRef<HTMLDivElement>();

  public dialogRef = React.createRef<HTMLDivElement>();

  public searchEngineInputRef = React.createRef<Textfield>();
  public searchEngineKeywordInputRef = React.createRef<Textfield>();
  public searchEngineUrlInputRef = React.createRef<Textfield>();

  @observable
  public menuInfo = {
    left: 0,
    top: 0,
  };

  @observable
  private _menuVisible = false;

  @computed
  public get menuVisible() {
    return this._menuVisible;
  }

  public set menuVisible(value: boolean) {
    this._menuVisible = value;

    if (value) {
      this.menuRef.current.focus();
    }
  }

  @observable
  public dialogVisible = false;

  @observable
  public dialogContent:
    | 'edit-search-engine'
    | 'add-search-engine'
    | 'edit-address'
    | 'edit-password'
    | 'privacy' = null;

  @observable
  public selectedSection: SettingsSection = 'appearance';

  @observable
  public searchValue = '';

  @observable
  public settings: ISettings = {
    ...DEFAULT_SETTINGS,
    ...(window as any).settings
  } as ISettings;

  @observable
  public appVersion: string = (window as any)['xiu-app-version'];

  @observable
  public editedSearchEngine: ISearchEngine = null;

  @computed
  public get theme(): ITheme {
    return getTheme(this.settings.theme);
  }

  @computed
  public get searchEngine() {
    return this.settings.searchEngines[this.settings.searchEngine];
  }

  constructor() {
    makeObservable(this, {
      selectedSection: observable,
      searchValue: observable,
      settings: observable,
      dialogContent: observable,
      dialogVisible: observable,
      menuInfo: observable,
      _menuVisible: observable,
      appVersion: observable,
      editedSearchEngine: observable,
      menuVisible: computed,
      theme: computed,
      searchEngine: computed,
      updateDownloadsPath: action,
      setDialogContent: action,
      search: action,
    });

    (window as any).updateSettings = (settings: ISettings) => {
      const prevTheme = this.settings.theme;
      const prevThemeAuto = this.settings.themeAuto;
      this.settings = { ...this.settings, ...settings };

      // 如果主题或自动模式发生变化，更新Tailwind主题
      if (prevTheme !== settings.theme || prevThemeAuto !== settings.themeAuto) {
        console.log('[Settings Store] Theme changed from', prevTheme, 'to', settings.theme, 'themeAuto:', settings.themeAuto);
        TailwindThemeManager.setThemeWithAuto(settings.theme, settings.themeAuto);
      }
    };

    window.onmousedown = () => {
      this.autoFill.menuVisible = false;
    };

    // 广告拦截对话框现在由主进程直接处理
  }

  @action
  public updateDownloadsPath(newPath: string) {
    console.log('[Settings Store] Updating downloads path from', this.settings.downloadsPath, 'to', newPath);

    // 创建新的 settings 对象以确保 MobX 检测到变化
    this.settings = {
      ...this.settings,
      downloadsPath: newPath
    };

    console.log('[Settings Store] Downloads path updated successfully');
  }

  @action
  public setDialogContent(content: typeof this.dialogContent) {
    console.log('[Settings Store] Setting dialogContent from', this.dialogContent, 'to', content);
    this.dialogContent = content;
  }





  @action
  public search(value: string) {
    this.searchValue = value;
  }
  public save() {
    delete this.settings.darkContents;
    delete this.settings.multrin;
    delete this.settings.shield;

    window.postMessage(
      {
        type: 'save-settings',
        data: JSON.stringify(this.settings),
      },
      '*',
    );
  }
}

export default new Store();
