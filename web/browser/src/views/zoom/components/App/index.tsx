import * as React from 'react';
import { observer } from 'mobx-react-lite';

import { ToolbarButton } from '../../../app/components/ToolbarButton';
import store from '../../store';
import { Button } from '@browser/core/components/Button';
import { eventUtils } from '@browser/core/utils/platform-lite';
import { UIStyle } from '@browser/core/styles/default-styles';
import { cn } from '@browser/utils/tailwind-helpers';

import { ICON_UP, ICON_DOWN } from '@mario-ai/shared';

const onPlus = () => {
  eventUtils.send('change-zoom', 'in');
};

const onMinus = () => {
  eventUtils.send('change-zoom', 'out');
};

const onReset = () => {
  eventUtils.send('reset-zoom');
};

eventUtils.on('zoom-factor-updated', (e, zoomFactor) => {
  store.zoomFactor = zoomFactor;
});

export const App = observer(() => {
  // StyledApp 样式 - Tailwind 版本 (继承 DialogStyle)
  const appClasses = cn(
    // DialogStyle 基础样式
    'p-4 rounded-[10px] shadow-dialog bg-mario-dialog',
    // 动画效果
    'animate-[fadeIn_0.15s_ease-out]',
    // 文本颜色根据主题
    store.theme['dialog.lightForeground'] ? 'text-white' : 'text-black'
  );

  // Label 样式
  const labelClasses = cn(
    'text-base min-w-[45px] text-center' // font-size: 16px, min-width: 45px
  );

  // Buttons 样式
  const buttonsClasses = cn(
    'w-full flex items-center justify-end',
    // 子元素 button 的右边距 (除了最后一个)
    '[&_.button:not(:last-child)]:mr-2'
  );

  return (
    <div
      className={appClasses}
      onMouseEnter={() => store.stopHideTimer()}
      onMouseLeave={() => store.resetHideTimer()}
    >
      <UIStyle />
      <div className={buttonsClasses}>
        <ToolbarButton
          toggled={false}
          icon={ICON_UP}
          size={18}
          dense
          iconStyle={{ transform: 'scale(-1,1)' }}
          onClick={onPlus}
        />
        <div className={labelClasses}>
          {(store.zoomFactor * 100).toFixed(0) + '%'}
        </div>
        <ToolbarButton
          toggled={false}
          icon={ICON_DOWN}
          size={18}
          dense
          iconStyle={{ transform: 'scale(-1,1)' }}
          onClick={onMinus}
        />
        <div className="flex-grow" /> {/* Spacer */}
        <Button
          onClick={onReset}
          background={
            store.theme['dialog.lightForeground']
              ? 'rgba(255, 255, 255, 0.08)'
              : 'rgba(0, 0, 0, 0.08)'
          }
          foreground={
            store.theme['dialog.lightForeground'] ? 'white' : 'black'
          }
        >
          Reset
        </Button>
      </div>
    </div>
  );
});
