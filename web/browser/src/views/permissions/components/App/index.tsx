import * as React from 'react';
import { observer } from 'mobx-react-lite';

import store from '../../store';
import { Button } from '@browser/core/components/Button';
import { UIStyle } from '@browser/core/styles/default-styles';
import { cn } from '@browser/utils/tailwind-helpers';

const sendResult = (r: boolean) => {
  store.send('result', r);
};

const getText = (permission: string) => {
  if (permission === 'notifications') {
    return '发送通知消息';
  }

  if (permission === 'microphone') {
    return '使用麦克风';
  }

  if (permission === 'camera') {
    return '使用摄像头';
  }

  if (permission === 'geolocation') {
    return '获取位置信息';
  }

  return "获取" + permission + "权限";
};

export const App = observer(() => {
  // StyledApp 样式 - Tailwind 版本
  const appClasses = cn(
    'm-4 p-4 rounded-[10px] shadow-dialog overflow-hidden relative',
    'bg-mario-dialog',
    store.theme['dialog.lightForeground'] ? 'text-white' : 'text-black'
  );

  // Title 样式
  const titleClasses = cn(
    'text-base font-light' // font-size: 16px, font-weight: 300
  );

  // Permissions 样式
  const permissionsClasses = cn('mt-3'); // margin-top: 12px

  // Permission 样式
  const permissionClasses = cn(
    'text-sm mt-2' // font-size: 13px, margin-top: 8px
  );

  // Buttons 样式
  const buttonsClasses = cn(
    'flex mt-4 float-right' // margin-top: 16px
  );

  return (
    <div className={appClasses}>
      <UIStyle />
      <div className={titleClasses}>网站 {store.domain} 想要：</div>
      <div className={permissionsClasses}>
        {store.permissions.map((item) => (
          <div key={item} className={permissionClasses}>
            {getText(item)}
          </div>
        ))}
      </div>
      <div className={buttonsClasses}>
        <Button
          background={
            store.theme['dialog.lightForeground']
              ? 'rgba(255, 255, 255, 0.08)'
              : 'rgba(0, 0, 0, 0.08)'
          }
          foreground={
            store.theme['dialog.lightForeground'] ? 'white' : 'black'
          }
          onClick={() => sendResult(true)}
        >
          允许
        </Button>
        <Button
          background={
            store.theme['dialog.lightForeground']
              ? 'rgba(255, 255, 255, 0.08)'
              : 'rgba(0, 0, 0, 0.08)'
          }
          foreground={
            store.theme['dialog.lightForeground'] ? 'white' : 'black'
          }
          style={{ marginLeft: 8 }}
          onClick={() => sendResult(false)}
        >
          拒绝
        </Button>
      </div>
      <div style={{ clear: 'both' }}></div>
    </div>
  );
});
