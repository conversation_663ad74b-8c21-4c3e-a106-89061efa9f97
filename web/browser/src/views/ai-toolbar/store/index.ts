import { eventUtils } from '@browser/core/utils/platform-lite';

export class AIToolbarStore {
  public windowId: number = 1;

  public constructor() {
    // 获取窗口ID
    try {
      this.windowId = eventUtils.sendSync('get-window-id') || 1;
      console.log('[AIToolbarStore] Window ID obtained:', this.windowId);
    } catch (error) {
      console.warn('[AIToolbarStore] Failed to get window ID, using default:', error);
      this.windowId = 1;
    }
  }
}

export default new AIToolbarStore();
