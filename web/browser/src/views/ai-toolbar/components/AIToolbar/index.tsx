import * as React from 'react';
import { observer } from 'mobx-react-lite';

import { cn } from '@browser/utils/tailwind-helpers';
import { UIStyle } from '@browser/core/styles/default-styles';
import { eventUtils } from '@browser/core/utils/platform-lite';
import store from '../../store';

// AI工具定义
interface AITool {
  id: string;
  name: string;
  icon: string;
  description: string;
}

const AI_TOOLS: AITool[] = [
  {
    id: 'notes',
    name: '智能笔记',
    icon: '📝',
    description: '创建和管理智能笔记'
  },
  {
    id: 'memory',
    name: 'AI记忆',
    icon: '🧠',
    description: '存储和检索重要信息'
  },
  {
    id: 'clipboard',
    name: '剪贴板',
    icon: '📋',
    description: '智能剪贴板管理'
  }
];

export const AIToolbar = observer(() => {

  // 工具栏样式 - 通栏显示
  const toolbarClasses = cn(
    'flex flex-col',
    'w-16 h-screen',
    'bg-mario-toolbar',
    'border-r border-mario-toolbar-separator',
    'shadow-sm'
  );

  // Logo区域样式 - 为macOS窗口控制按钮预留空间
  const logoClasses = cn(
    'flex items-center justify-center',
    'h-8 w-full',
    'mt-6 mb-4', // 为窗口控制按钮预留空间
    'text-mario-text opacity-60'
  );

  // 工具按钮样式
  const getToolButtonClasses = () => cn(
    'flex flex-col items-center justify-center',
    'w-12 h-12 mx-2 mb-3',
    'rounded-lg transition-all duration-200',
    'cursor-pointer select-none',
    'hover:bg-mario-toolbar-button-hover',
    'bg-transparent'
  );

  // 设置按钮样式 - 底部固定
  const settingsButtonClasses = cn(
    'flex items-center justify-center',
    'w-12 h-12 mx-2 mb-4',
    'rounded-lg transition-all duration-200',
    'cursor-pointer select-none',
    'hover:bg-mario-toolbar-button-hover',
    'mt-auto' // 推到底部
  );

  const handleToolClick = async (toolId: string) => {
    console.log('[AIToolbar] Tool clicked:', toolId);

    // 通过IPC通知主进程处理工具点击
    try {
      await eventUtils.invoke(`ai-toolbar-tool-click-${store.windowId}`, toolId);
    } catch (error) {
      console.error('[AIToolbar] Failed to handle tool click:', error);
    }
  };

  const handleSettingsClick = () => {
    console.log('[AIToolbar] Settings clicked');
    // TODO: 打开AI设置页面
  };

  // 通知主进程工具栏已准备就绪
  React.useEffect(() => {
    eventUtils.send('ai-toolbar-ready');
  }, []);

  return (
    <div className={toolbarClasses}>
      <UIStyle />
      
      {/* Logo区域 */}
      <div className={logoClasses}>
        🤖
      </div>

      {/* 工具按钮区域 */}
      <div className="flex-1 flex flex-col">
        {AI_TOOLS.map((tool) => (
          <div
            key={tool.id}
            className={getToolButtonClasses()}
            onClick={() => handleToolClick(tool.id)}
            title={tool.description}
          >
            <span className="text-lg">{tool.icon}</span>
          </div>
        ))}
      </div>

      {/* 设置按钮 - 底部 */}
      <div
        className={settingsButtonClasses}
        onClick={handleSettingsClick}
        title="AI设置"
      >
        <span className="text-lg">⚙️</span>
      </div>
    </div>
  );
});

export default AIToolbar;
