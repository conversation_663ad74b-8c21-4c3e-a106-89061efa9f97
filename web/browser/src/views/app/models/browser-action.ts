import { observable, computed, makeObservable } from 'mobx';
import { EXTENSIONS_PROTOCOL } from '@mario-ai/shared';
// 使用浏览器兼容的URL构建方式

interface Options {
  icon: string;
  title: string;
  popup: string;
  extensionId: string;
}

export class IBrowserAction {
  // Observable
  public icon?: string = '';

  public _popup?: string = '';

  public title?: string = '';

  public badgeBackgroundColor?: string = 'gray';

  public badgeTextColor?: string = 'white';

  public badgeText?: string = '';

  // Computed
  public get popup() {
    return this._popup;
  }
  // ---

  public set popup(url: string) {
    if (!url) {
      this._popup = null;
    } else if (url.startsWith(EXTENSIONS_PROTOCOL)) {
      this._popup = url;
    } else {
      // 使用字符串拼接替代Node.js的url.format
      this._popup = `${EXTENSIONS_PROTOCOL}//${this.extensionId}${url.startsWith('/') ? url : '/' + url}`;
    }
  }

  public tabId?: number;

  public extensionId?: string;

  public wasOpened = false;

  public constructor(options: Options) {
    makeObservable(this, {
      icon: observable,
      _popup: observable,
      title: observable,
      badgeBackgroundColor: observable,
      badgeText: observable,
      badgeTextColor: observable,
      popup: computed,
    });

    const { icon, title, extensionId, popup } = options;
    this.icon = icon;
    this.title = title;
    this.extensionId = extensionId;
    this.popup = popup;
  }
}