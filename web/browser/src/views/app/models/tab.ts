// 移除electron直接依赖
import {observable, computed, action, makeObservable} from 'mobx';
import * as React from 'react';

import store from '../store';
import {
  TABS_PADDING,
  TAB_ANIMATION_DURATION,
  TAB_MIN_WIDTH,
  TAB_MAX_WIDTH,
  TAB_PINNED_WIDTH,
} from '../constants';
import { viewUtils, tabUtils, ipcRenderer, eventUtils } from '@browser/core/utils/platform-lite';
import {animateTab} from '../utils/tabs';
import {getWebUIURL} from '@browser/core/utils/webui';

export class ITab {
  public id = -1;

  public addressbarFocused = false;
  public addressbarSelectionRange = [0, 0];

  public width = 0;
  public left = 0;

  public lastUrl = '';
  public isClosing = false;
  public ref = React.createRef<HTMLDivElement>();

  public removeTimeout: any;

  public marginLeft = 0;

  // Observable

  public isDragging = false;

  public isPinned = false;

  public isMuted = false;

  public isPlaying = false;

  public title = '新标签页';

  public loading = true;

  public favicon = '';

  public tabGroupId = -1;

  public addressbarValue: string = '';

  public url = '';

  public blockedAds: any[] = [];

  public videoUrls: string[] = [];
  public filtersCount = 0;

  public hasCredentials = false;

  public isBookmarked = false;

  // Computed
  public get isSelected() {
    return store.tabs.selectedTabId === this.id;
  }

  public get isHovered() {
    return store.tabs.hoveredTabId === this.id;
  }

  public get isExpanded() {
    return this.isHovered || this.isSelected || !store.tabs.scrollable;
  }

  public get isIconSet() {
    return this.favicon !== '' || this.loading;
  }

  public constructor(
    {active, url, pinned}: chrome.tabs.CreateProperties,
    id: number,
  ) {
    makeObservable(this, {
      addressbarValue: observable,
      url: observable,
      favicon: observable,
      loading: observable,
      tabGroupId: observable,
      isDragging: observable,
      isPinned: observable,
      isMuted: observable,
      isPlaying: observable,
      title: observable,
      blockedAds: observable,
      videoUrls: observable,
      hasCredentials: observable,
      isBookmarked: observable,
      isSelected: computed,
      isHovered: computed,
      isExpanded: computed,
      isIconSet: computed,
      select: action,
      close: action,
      updateData: action,
      setFavicon: action,
      setLoading: action,
      setTitle: action,
      setIsPlaying: action,
      setIsPinned: action,
      setHasCredentials: action,
      setVideoUrls: action,
      setIsBookmarked: action,
    });

    this.url = url;
    this.addressbarValue = url || ''; // 初始化地址栏值
    this.id = id;
    this.isPinned = pinned;

    if (getWebUIURL("newtab").startsWith(url)) {
      this.addressbarFocused = true;
      this.addressbarValue = '';
    } else {
      this.addressbarValue = url;
    }

    if (active) {
      requestAnimationFrame(() => {
        this.select();
      });
    }

    if (process.env.ENABLE_EXTENSIONS) {
      const {defaultBrowserActions} = store.extensions;

      for (const item of defaultBrowserActions) {
        store.extensions.addBrowserActionToTab(this.id, item);
      }
    }
  }

  @action
  public async updateData() {
    if (!store.isIncognito) {
      try {
        // ✅ 重构: 避免在tab-event处理中发送IPC消息，防止序列化冲突 (修复序列化错误)
        // 使用setTimeout延迟执行，避免在事件处理中直接发送IPC
        setTimeout(async () => {
          try {
            const tabIndex = store.tabs.list.findIndex(tab => tab.id === this.id);
            await store.startupTabs.addStartupTabItem({
              id: this.id,
              windowId: store.windowId,
              url: this.url,
              favicon: this.favicon,
              pinned: !!this.isPinned,
              title: this.title,
              isUserDefined: false,
              order: tabIndex >= 0 ? tabIndex : 0,
            });
          } catch (error) {
            console.warn('[Tab] Failed to update startup tab data:', error);
          }
        }, 0);
      } catch (error) {
        console.warn('[Tab] Failed to schedule startup tab update:', error);
      }
    }
  }

  public get tabGroup() {
    return store.tabGroups.getGroupById(this.tabGroupId);
  }

  @action
  public async select() {
    if (!this.isClosing) {
      store.tabs.selectedTabId = this.id;

      // 同步addressbarValue到store，如果地址栏没有焦点，显示当前页面URL
      if (!store.addressbarFocused) {
        const displayValue = this.url || '';
        store.addressbarValue = displayValue;
        this.addressbarValue = displayValue;
        // 更新URL分段显示
        store.addressbarUrlSegments = store.tabs.parseUrlSegments(displayValue);
      } else {
        // 如果地址栏有焦点，保持当前输入的值
        store.addressbarValue = this.addressbarValue || '';
      }

      // 恢复原始工程的双重调用机制
      // 第一步：发送browserview-show事件（同步）
      if (ipcRenderer) {
        // 使用eventUtils.send确保正确的序列化处理
        eventUtils.send(`browserview-show-${store.windowId}`);
      }

      const focused = this.addressbarFocused;

      // 第二步：调用view-select处理器（异步）
      if (ipcRenderer) {
        try {
          await ipcRenderer.invoke(`view-select-${store.windowId}`, this.id, !this.addressbarFocused);
        } catch (error) {
          console.warn('[Platform] View select failed:', error);
        }
      }

      if (focused) {
        store.inputRef.focus();
        store.inputRef.setSelectionRange(
          this.addressbarSelectionRange[0],
          this.addressbarSelectionRange[1],
        );
      }
    }
  }

  public getWidth(containerWidth: number = null, tabs: ITab[] = null) {
    if (this.isPinned) return TAB_PINNED_WIDTH;

    if (containerWidth === null) {
      containerWidth = store.tabs.containerWidth;
    }

    if (tabs === null) {
      tabs = store.tabs.list.filter((x) => !x.isClosing);
    }

    const pinnedTabs = tabs.filter((x) => x.isPinned).length;

    const realTabsLength = tabs.length - pinnedTabs + store.tabs.removedTabs;

    const width =
      (containerWidth - pinnedTabs * (TAB_PINNED_WIDTH + TABS_PADDING)) /
      realTabsLength -
      TABS_PADDING -
      store.tabs.leftMargins / realTabsLength;

    // 修复：使用更合理的Tab宽度
    if (width > TAB_MAX_WIDTH) {
      return TAB_MAX_WIDTH;
    }

    // 设置更合理的最小宽度
    const REASONABLE_MIN_WIDTH = 120; // 增加最小宽度到120px
    if (width < REASONABLE_MIN_WIDTH) {
      return REASONABLE_MIN_WIDTH;
    }

    // 限制最大宽度为240px，给Tab更多空间
    const reasonableWidth = Math.min(width, 240);
    return reasonableWidth;
  }

  public getLeft(calcNewLeft = false) {
    const tabs = store.tabs.list.filter((x) => !x.isClosing).slice();

    const index = tabs.indexOf(this);

    let left = 0;

    if (calcNewLeft) store.tabs.calculateTabMargins();

    for (let i = 0; i < index; i++) {
      left +=
        (calcNewLeft ? tabs[i].getWidth() : tabs[i].width) +
        TABS_PADDING +
        tabs[i].marginLeft;
    }

    return left + this.marginLeft;
  }

  public removeFromGroup() {
    if (!this.tabGroup) return;

    if (this.tabGroup.tabs.length === 1) {
      store.tabGroups.list = store.tabGroups.list.filter(
        (x) => x.id !== this.tabGroupId,
      );
    }

    this.tabGroupId = -1;
    store.tabs.updateTabsBounds(true);
  }

  @action
  public setLeft(left: number, animation: boolean) {
    animateTab('translateX', left, this.ref.current, animation);
    this.left = left;
  }

  @action
  public setWidth(width: number, animation: boolean) {
    animateTab('width', width, this.ref.current, animation);
    this.width = width;
  }

  @action
  public close() {
    store.tabs.closedUrl = this.url;
    store.tabs.canShowPreview = false;
    tabUtils.hidePreview(this.id);

    const selected = store.tabs.selectedTabId === this.id;

    store.startupTabs.removeStartupTabItem(this.id);

    tabUtils.close(this.id);

    const notClosingTabs = store.tabs.list.filter((x) => !x.isClosing);
    let index = notClosingTabs.indexOf(this);

    if (store.tabs.list.length === 1) {
      //closeWindow();
      store.tabs.addTab();
    }

    this.isClosing = true;
    if (notClosingTabs.length - 1 === index) {
      const previousTab = store.tabs.list[index - 1];
      if (previousTab) {
        this.setLeft(previousTab.getLeft(true) + this.getWidth(), true);
      }
      store.tabs.updateTabsBounds(true);
    } else {
      store.tabs.removedTabs++;
    }

    this.removeFromGroup();

    this.setWidth(0, true);
    store.tabs.setTabsLefts(true);
    store.tabs.setTabGroupsLefts(true);

    if (selected) {
      index = store.tabs.list.indexOf(this);

      if (index - 1 >= 0 && !store.tabs.list[index - 1].isClosing) {
        const prevTab = store.tabs.list[index - 1];
        prevTab.select();
      } else if (
        index + 1 < store.tabs.list.length &&
        !store.tabs.list[index + 1].isClosing &&
        !store.tabs.scrollable
      ) {
        const nextTab = store.tabs.list[index + 1];
        nextTab.select();
      }
    }

    this.removeTimeout = setTimeout(() => {
      store.tabs.removeTab(this.id);
    }, TAB_ANIMATION_DURATION);
  }

  public callViewMethod = (scope: string, ...args: any[]): Promise<any> => {
    return viewUtils.callMethod(String(this.id), scope, ...args);
  };

  // Action方法
  @action
  public setFavicon(favicon: string) {
    this.favicon = favicon;
  }

  @action
  public setLoading(loading: boolean) {
    this.loading = loading;
  }

  @action
  public setTitle(title: string) {
    this.title = title;
  }

  @action
  public setIsPlaying(playing: boolean) {
    this.isPlaying = playing;
  }

  @action
  public setIsPinned(pinned: boolean) {
    this.isPinned = pinned;
  }

  @action
  public setHasCredentials(hasCredentials: boolean) {
    this.hasCredentials = hasCredentials;
  }

  @action
  public setVideoUrls(urls: string[]) {
    this.videoUrls = urls;
  }

  @action
  public setIsBookmarked(isBookmarked: boolean) {
    this.isBookmarked = isBookmarked;
  }
}
