import { observer } from 'mobx-react-lite';
import * as React from 'react';
import { viewUtils } from '@browser/core/utils/platform-lite';
import { eventUtils } from '@browser/core/utils/platform-lite';
import { Preloader } from '@browser/core/components/Preloader';
import { ICON_VOLUME_HIGH, ICON_VOLUME_OFF, ICON_CLOSE, transparency } from '@mario-ai/shared';
import { ITab, ITabGroup } from '../../models';
import store from '../../store';
import { COMPACT_TAB_MARGIN_TOP } from '@mario-ai/shared';
import { TAB_PINNED_WIDTH } from '../../constants';
import { cn } from '@browser/utils/tailwind-helpers';

const removeTab = (tab: ITab) => (e: React.MouseEvent<HTMLDivElement>) => {
  e.stopPropagation();
  tab.close();
};

const toggleMuteTab = (tab: ITab) => (e: React.MouseEvent<HTMLDivElement>) => {
  e.stopPropagation();
  tab.isMuted ? store.tabs.unmuteTab(tab) : store.tabs.muteTab(tab);
};

const onCloseMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
  e.stopPropagation();
};

const onVolumeMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
  e.stopPropagation();
};

const onMouseDown = (tab: ITab) => (e: React.MouseEvent<HTMLDivElement>) => {
  const { pageX, button } = e;

  console.log("onMouseDown", store.addressbarEditing);
  if (store.addressbarEditing) {
    store.inputRef.focus();
  }

  if (button === 0) {
    if (!tab.isSelected) {
      tab.select();
    } else {
      store.canOpenSearch = true;
    }

    store.tabs.lastMouseX = 0;
    store.tabs.isDragging = true;
    store.tabs.mouseStartX = pageX;
    store.tabs.tabStartX = tab.left;

    store.tabs.lastScrollLeft = store.tabs.containerRef.current.scrollLeft;
  }

  eventUtils.send(`hide-tab-preview-${store.windowId}`);
};

const onMouseEnter = (tab: ITab) => (e: React.MouseEvent<HTMLDivElement>) => {
  if (!store.tabs.isDragging) {
    store.tabs.hoveredTabId = tab.id;
  }

  const { bottom, left } = tab.ref.current.getBoundingClientRect();

  const x = left + 8;
  const y = store.isCompact ? bottom - COMPACT_TAB_MARGIN_TOP : bottom;

  if (store.tabs.canShowPreview && !store.tabs.isDragging) {
    // 使用轻量级工具替代
    console.log('[Lite] Show tab preview:', {
      id: tab.id,
      x,
      y,
    });
  }
};

const onMouseLeave = () => {
  store.tabs.hoveredTabId = -1;
  eventUtils.send(`hide-tab-preview-${store.windowId}`);
  store.tabs.canShowPreview = true;
};

const onClick = (tab: ITab) => (e: React.MouseEvent<HTMLDivElement>) => {
  if (e.button === 4) {
    tab.close();
    return;
  }

  if (store.isCompact && e.button === 0 && store.canOpenSearch) {
    store.inputRef.focus();
    store.canOpenSearch = false;
  }
};

const onMouseUp = (tab: ITab) => (e: React.MouseEvent<HTMLDivElement>) => {
  if (e.button === 1) {
    tab.close();
  }
};

const onContextMenu = (tab: ITab) => () => {
  const tabGroups: ITabGroup[] = store.tabGroups
    .getGroups()
    .filter((t) => t.id !== tab.tabGroupId);

  const menu = remote.Menu.buildFromTemplate([
    {
      label: '在右侧新增标签页',
      click: () => {
        store.tabs.addTab(
          {
            index: store.tabs.list.indexOf(store.tabs.selectedTab) + 1,
          },
          tab.tabGroupId,
        );
      },
    },
    {
      label: '添加到新分组',
      visible: tabGroups.length === 0,
      click: () => {
        addTabToNewGroup(tab);
      },
    },
    {
      label: '添加到分组',
      visible: tabGroups.length > 0,
      submenu: tabGroupSubmenu(tab, tabGroups),
    },
    {
      label: '从分组中移除',
      visible: !!tab.tabGroup,
      click: () => {
        tab.removeFromGroup();
      },
    },
    {
      type: 'separator',
    },
    {
      label: '刷新',
      accelerator: 'CmdOrCtrl+R',
      click: () => {
        viewUtils.reload(tab.id);
      },
    },
    {
      label: '复制',
      click: () => {
        store.tabs.addTab({ active: true, url: tab.url });
      },
    },
    {
      label: tab.isPinned ? '取消固定' : '固定',
      click: () => {
        tab.isPinned ? store.tabs.unpinTab(tab) : store.tabs.pinTab(tab);
      },
    },
    {
      label: tab.isMuted ? '取消静音' : '静音',
      click: () => {
        tab.isMuted ? store.tabs.unmuteTab(tab) : store.tabs.muteTab(tab);
      },
    },
    {
      type: 'separator',
    },
    {
      label: '关闭',
      accelerator: 'CmdOrCtrl+W',
      click: () => {
        tab.close();
      },
    },
    {
      label: '关闭其它标签',
      click: () => {
        for (const t of store.tabs.list) {
          if (t !== tab) {
            t.close();
          }
        }
      },
    },
    {
      label: '关闭左侧标签',
      click: () => {
        for (let i = store.tabs.list.indexOf(tab) - 1; i >= 0; i--) {
          store.tabs.list[i].close();
        }
      },
    },
    {
      label: '关闭右侧标签',
      click: () => {
        for (
          let i = store.tabs.list.length - 1;
          i > store.tabs.list.indexOf(tab);
          i--
        ) {
          store.tabs.list[i].close();
        }
      },
    },
    {
      type: 'separator',
    },
    {
      label: '撤销关闭的标签',
      enabled: store.tabs.closedUrl !== '',
      click: () => {
        store.tabs.revertClosed();
      },
    },
  ]);

  menu.popup();
};

const addTabToNewGroup = (tab: ITab): void => {
  tab.removeFromGroup();
  const tabGroup = store.tabGroups.addGroup();
  tab.tabGroupId = tabGroup.id;
  store.tabs.updateTabsBounds(true);
};

const tabGroupSubmenu = (tab: ITab, tabGroups: ITabGroup[]): Menu => {
  return remote.Menu.buildFromTemplate([
    {
      label: '创建分组',
      click: () => {
        addTabToNewGroup(tab);
      },
    },
    {
      type: 'separator',
    },
    ...tabGroups.map((tabGroup) => ({
      label: tabGroupLabel(tabGroup),
      icon: tabGroupIcon(tabGroup.color),
      click: () => {
        store.tabs.setTabToGroup(tab, tabGroup.id);
      },
    })),
  ]);
};

const tabGroupLabel = (tabGroup: ITabGroup): string => {
  const tabs = store.tabs.list.filter((x) => x.tabGroupId === tabGroup.id);
  const tabsLength = tabs.length;
  const tabTitle = tabs[0].title;

  let label =
    tabGroup.name ||
    `"${tabTitle.substr(0, 20)}${tabTitle.length > 20 ? '... ' : ''}"`;

  if (!tabGroup.name && tabsLength > 1) {
    label += ` and ${tabsLength - 1} other tabs`;
  }
  return label;
};

const tabGroupIcon = (color: string): nativeImage => {
  var canvas = document.createElement('canvas');
  var context = canvas.getContext('2d');
  canvas.width = canvas.height = 12;

  context.fillStyle = color;
  context.beginPath();
  context.ellipse(
    canvas.width / 2,
    canvas.height / 2,
    canvas.width / 2,
    canvas.height / 2,
    0,
    0,
    Math.PI * 2,
  );
  context.fill();
  return nativeImage.createFromDataURL(canvas.toDataURL());
};

const Content = observer(({ tab }: { tab: ITab }) => {


  // StyledContent 样式 - Tailwind 版本
  const contentClasses = cn(
    'overflow-hidden z-[2] items-center flex ml-2.5 flex-1'
  );

  // StyledIcon 样式 - Tailwind 版本
  const iconClasses = cn(
    'h-4 min-w-4 transition-all duration-200 bg-center bg-no-repeat bg-contain opacity-100'
  );

  // StyledTitle 样式 - Tailwind 版本
  const titleClasses = cn(
    'text-xs overflow-hidden text-ellipsis whitespace-nowrap transition-all duration-200',
    'min-w-0 flex-1',
    // 左边距根据是否有图标
    !tab.isIconSet ? 'ml-0' : 'ml-3'
  );

  const titleStyle = {
    color: tab.isSelected
      ? store.theme['tab.selected.textColor']
      : store.theme['tab.textColor']
  };

  return (
    <div className={contentClasses}>
      {!tab.loading && tab.favicon !== '' && (
        <div
          className={iconClasses}
          style={{ backgroundImage: `url(${tab.favicon})` }}
        >
          <PinnedVolume tab={tab} />
        </div>
      )}

      {tab.loading && (
        <Preloader
          color={store.theme.accentColor}
          thickness={6}
          size={16}
          indeterminate
          style={{ minWidth: 16 }}
        />
      )}
      {!tab.isPinned && (
        <div className={titleClasses} style={titleStyle}>
          {tab.isSelected && store.isCompact ? tab.url : tab.title}
        </div>
      )}
      <ExpandedVolume tab={tab} />
      <Close tab={tab} />
    </div>
  );
});

const ExpandedVolume = observer(({ tab }: { tab: ITab }) => {
  const visible = tab.isExpanded && !tab.isPinned && tab.isPlaying;
  const icon = tab.isMuted ? ICON_VOLUME_OFF : ICON_VOLUME_HIGH;

  // StyledAction 样式 - Tailwind 版本
  const actionClasses = cn(
    'h-5 w-5 ml-0.5 rounded-sm transition-colors duration-100 z-10',
    'bg-center bg-no-repeat bg-contain',
    'hover:bg-black/10',
    // 可见性控制
    visible ? 'block' : 'hidden',
    // 透明度 - 使用标准Tailwind类
    visible ? 'opacity-60' : 'opacity-0' // 0.6 = opacity-60
  );

  const actionStyle = {
    backgroundImage: `url(${icon})`,
    filter: store.theme['toolbar.lightForeground'] ? 'invert(100%)' : 'none',
    opacity: transparency.icons.inactive // 恢复原始透明度 0.6
  };

  if (!visible) return null;

  return (
    <div
      className={actionClasses}
      style={actionStyle}
      onMouseDown={onVolumeMouseDown}
      onClick={toggleMuteTab(tab)}
    />
  );
});

const PinnedVolume = observer(({ tab }: { tab: ITab }) => {
  const visible = tab.isPinned && tab.isPlaying;
  const icon = tab.isMuted ? ICON_VOLUME_OFF : ICON_VOLUME_HIGH;

  // StyledPinAction 样式 - Tailwind 版本
  const pinActionClasses = cn(
    'h-3 w-3 rounded-full transition-colors duration-100 z-10',
    'fixed right-2 top-2 bg-center bg-no-repeat bg-contain',
    'hover:invert',
    // 可见性控制
    visible ? 'block' : 'hidden'
  );

  const pinActionStyle = {
    backgroundColor: store.theme['toolbar.lightForeground'] ? 'rgb(255, 255, 255)' : 'rgb(0, 0, 0)',
    backgroundImage: `url(${icon})`
  };

  if (!visible) return null;

  return (
    <div
      className={pinActionClasses}
      style={pinActionStyle}
      onMouseDown={onVolumeMouseDown}
      onClick={toggleMuteTab(tab)}
    />
  );
});

const Close = observer(({ tab }: { tab: ITab }) => {
  const visible = tab.isExpanded && !tab.isPinned;

  // StyledClose 样式 - Tailwind 版本
  const closeClasses = cn(
    'h-5 w-5 ml-0.5 mr-1.5 rounded-full transition-colors duration-100 z-10',
    'bg-center bg-no-repeat bg-contain',
    'hover:bg-black/10',
    // 可见性控制
    visible ? 'block' : 'hidden',
    // 透明度 - 使用标准Tailwind类
    visible ? 'opacity-60' : 'opacity-0' // 0.6 = opacity-60
  );

  const closeStyle = {
    backgroundImage: `url(${ICON_CLOSE})`,
    filter: store.theme['toolbar.lightForeground'] ? 'invert(100%)' : 'none',
    opacity: transparency.icons.inactive // 恢复原始透明度 0.6
  };

  if (!visible) return null;

  return (
    <div
      className={closeClasses}
      style={closeStyle}
      onMouseDown={onCloseMouseDown}
      onClick={removeTab(tab)}
    />
  );
});

export default observer(({ tab }: { tab: ITab }) => {
  const defaultColor = store.theme['toolbar.lightForeground']
    ? '#1c1c1c'
    : '#f3f3fa';

  const defaultSelectedColor = store.theme['toolbar.lightForeground']
    ? '#393939'
    : '#FFFFFF';



  // StyledTab 样式 - Tailwind 版本
  // 注意：不设置left-0和w-0类，让anime.js完全控制位置和宽度
  const tabClasses = cn(
    'absolute h-full flex backface-hidden',
    '[&]:[-webkit-app-region:no-drag]',
    // z-index 根据选中状态
    tab.isSelected ? 'z-[2]' : 'z-[1]',

  );

  // 重要：添加will-change属性和初始位置/宽度，确保anime.js能正确设置样式
  const tabStyle: React.CSSProperties = {
    willChange: 'width, transform',
    width: 0, // 初始宽度为0，由anime.js动态设置
    left: 0,  // 初始位置为0，由anime.js的transform控制最终位置
  };

  // Tab的宽度和位置由animateTab函数通过anime.js动态设置
  // 不需要在React中手动设置这些样式



  // Tab的宽度和位置完全由anime.js控制，不需要React样式

  // TabContainer 样式 - Tailwind 版本
  const containerClasses = cn(
    'relative w-full items-center overflow-hidden flex h-full backface-hidden',
    'transition-colors duration-100 rounded-lg',
    // 最大宽度根据是否固定
    tab.isPinned ? `max-w-[${TAB_PINNED_WIDTH}px]` : 'max-w-full',
    // 边框根据选中状态
    tab.isSelected ? 'border border-black/32' : 'border border-transparent'
  );

  const containerStyle = {
    marginTop: `${store.theme.tabMarginTop || 4}px`, // 添加fallback值
    marginBottom: '4px',
    height: '30px',
    backgroundColor: `${tab.isSelected ? defaultSelectedColor : defaultColor} !important`
  };




  return (
    <div
      className={tabClasses}
      style={tabStyle}
      onMouseDown={onMouseDown(tab)}
      onMouseUp={onMouseUp(tab)}
      onMouseEnter={onMouseEnter(tab)}
      onContextMenu={onContextMenu(tab)}
      onClick={onClick(tab)}
      onMouseLeave={onMouseLeave}
      ref={tab.ref}
    >
      <div
        className={containerClasses}
        style={containerStyle}
      >
        <Content tab={tab} />
      </div>
    </div>
  );
});
