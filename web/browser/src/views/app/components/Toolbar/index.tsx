import { observer } from 'mobx-react-lite';
import * as React from 'react';

import { NavigationButtons } from '../NavigationButtons';
import { AddressBar } from '../AddressBar';
import { RightButtons } from '../RightButtons';
import { cn } from '@browser/utils/tailwind-helpers';
import { TOOLBAR_HEIGHT } from '@mario-ai/shared';
import store from '../../store';

export const Toolbar = observer(() => {
  // StyledToolbar 样式 - Tailwind 版本
  const toolbarClasses = cn(
    'relative z-[100] flex items-center flex-row w-full pb-1',
    'text-black/80 bg-mario-toolbar border-b border-mario-toolbar-bottom-line'
  );

  const toolbarStyle: React.CSSProperties = {
    height: `${TOOLBAR_HEIGHT}px`,
  };

  return (
    <div className={toolbarClasses} style={toolbarStyle}>
      <NavigationButtons />
      <AddressBar />
      <RightButtons />
    </div>
  );
});
