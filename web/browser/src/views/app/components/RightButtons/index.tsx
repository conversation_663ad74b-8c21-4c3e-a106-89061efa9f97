import {observer} from 'mobx-react-lite';
import * as React from 'react';
import { eventUtils } from '@browser/core/utils/platform-lite';

import {ToolbarButton} from '../ToolbarButton';
import {BrowserAction} from '../BrowserAction';
import {
  ICON_SHIELD,
  ICON_DOWNLOAD,
  ICON_INCOGNITO,
  ICON_MORE,
} from '@mario-ai/shared';
import store from '../../store';
import {SiteButtons} from '../SiteButtons';
import { cn } from '@browser/utils/tailwind-helpers';

let menuRef: HTMLDivElement = null;
let downloadDialogRef: HTMLDivElement = null;

const showDownloadDialog = async () => {
  if (downloadDialogRef == null) {
    return;
  }

  const {right, bottom} = downloadDialogRef.getBoundingClientRect();
  store.downloadNotification = false;
  eventUtils.send(`show-downloads-dialog-${store.windowId}`, right, bottom);
};

// 避免重复注册事件监听器
let downloadEventListenerRegistered = false;
if (!downloadEventListenerRegistered) {
  eventUtils.on('show-download-dialog', () => {
    showDownloadDialog();
  });
  downloadEventListenerRegistered = true;
}

const hideDownloadDialog = async () => {
  eventUtils.send(`hide-downloads-dialog-${store.windowId}`);
};

const onDownloadsClick = async (e: React.MouseEvent<HTMLDivElement>) => {
  e.preventDefault();
  e.stopPropagation();

  // 检查下载对话框当前状态，实现切换功能
  if (store.dialogsVisibility['downloads']) {
    hideDownloadDialog();
  } else {
    showDownloadDialog();
  }
};

const showMenuDialog = async () => {
  if (!menuRef) {
    console.warn('[RightButtons] menuRef is null, cannot show menu');
    return;
  }

  console.log('[RightButtons] Showing menu dialog...');
  const {right, bottom} = menuRef.getBoundingClientRect();
  console.log('[RightButtons] Menu position:', { right, bottom });

  eventUtils.send(`show-menu-dialog-${store.windowId}`, right, bottom);
};

// 避免重复注册事件监听器
let menuEventListenerRegistered = false;
if (!menuEventListenerRegistered) {
  eventUtils.on('show-menu-dialog', () => {
    console.log('[RightButtons] Received show-menu-dialog event');
    showMenuDialog();
  });
  menuEventListenerRegistered = true;
}

const hideMenuDialog = async () => {
  console.log('[RightButtons] Hiding menu dialog...');
  eventUtils.send(`hide-menu-dialog-${store.windowId}`);
};

const onMenuClick = async (e: React.MouseEvent<HTMLDivElement>) => {
  console.log('[RightButtons] Menu button clicked');
  e.preventDefault();
  e.stopPropagation();

  // 检查菜单当前状态，实现切换功能
  if (store.dialogsVisibility['menu']) {
    console.log('[RightButtons] Menu is visible, hiding it');
    hideMenuDialog();
  } else {
    console.log('[RightButtons] Menu is hidden, showing it');
    showMenuDialog();
  }
};

const BrowserActions = observer(() => {
  const {selectedTabId} = store.tabs;

  return (
    <>
      {selectedTabId &&
        store.extensions.browserActions.map((item) => {
          if (item.tabId === selectedTabId) {
            return <BrowserAction data={item} key={item.extensionId}/>;
          }
          return null;
        })}
    </>
  );
});

export const RightButtons = observer(() => {
  // Buttons 样式 - Tailwind 版本
  // 参考工程: margin-right: 4px;
  const buttonsClasses = cn(
    'flex items-center mr-1'
  );

  // Separator 样式 - Tailwind 版本
  const separatorClasses = cn(
    'h-4 w-px ml-1 mr-1 bg-mario-toolbar-separator'
  );

  return (
    <div className={buttonsClasses}>
      <BrowserActions/>
      {store.extensions.browserActions.length > 0 && <div className={separatorClasses} />}
      {store.isCompact && (
        <>
          <SiteButtons/>
          <div className={separatorClasses} />
        </>
      )}

      {store.downloadsButtonVisible && (
        <ToolbarButton
          divRef={(r) => (downloadDialogRef = r)}
          size={18}
          badge={store.downloadNotification}
          onMouseDown={onDownloadsClick}
          toggled={store.dialogsVisibility['downloads-dialog']}
          icon={ICON_DOWNLOAD}
          badgeTop={9}
          badgeRight={9}
          preloader
          value={store.downloadProgress}
          tooltip="下载"
        ></ToolbarButton>
      )}
      {store.isIncognito && <ToolbarButton icon={ICON_INCOGNITO} size={18} tooltip="无痕模式"/>}
      <ToolbarButton
        divRef={(r) => (menuRef = r)}
        toggled={store.dialogsVisibility['menu']}
        badge={store.updateAvailable}
        badgeRight={10}
        badgeTop={6}
        onMouseDown={onMenuClick}
        icon={ICON_MORE}
        size={18}
        tooltip="更多"
      />
    </div>
  );
});
