import { observer } from 'mobx-react-lite';
import * as React from 'react';

import { cn } from '@browser/utils/tailwind-helpers';
import { Titlebar } from '../Titlebar';
import { Toolbar } from '../Toolbar';
import store from '../../store';
import { UIStyle } from '@browser/core/styles/default-styles';
import { BookmarkBar } from '../BookmarkBar';
import { TailwindThemeManager } from '@browser/core/utils/tailwind-theme-manager';


const onAppLeave = () => {
  store.barHideTimer = setTimeout(function () {
    if (
      Object.keys(store.dialogsVisibility).some(
        (k) => store.dialogsVisibility[k],
      )
    ) {
      onAppLeave();
    } else {
      store.titlebarVisible = false;
    }
  }, 500);
};

const onAppEnter = () => {
  clearTimeout(store.barHideTimer);
};

const onLineEnter = () => {
  store.titlebarVisible = true;
};



const App = observer(() => {
  console.log('[App] Rendering App component');

  // 初始化主题
  React.useEffect(() => {
    console.log('[App] Initializing theme:', store.settings.object.theme, 'themeAuto:', store.settings.object.themeAuto);
    TailwindThemeManager.setThemeWithAuto(store.settings.object.theme, store.settings.object.themeAuto);
  }, []);

  // 监听主题变化
  React.useEffect(() => {
    const currentTheme = store.settings.object.theme;
    const isAuto = store.settings.object.themeAuto;
    console.log('[App] Theme changed to:', currentTheme, 'themeAuto:', isAuto);
    TailwindThemeManager.setThemeWithAuto(currentTheme, isAuto);
  }, [store.settings.object.theme, store.settings.object.themeAuto]);

  // 初始化启动Tab - 确保有初始Tab被创建
  React.useEffect(() => {
    console.log('[App] Initializing startup tabs...');
    store.startupTabs.load().catch(error => {
      console.error('[App] Failed to load startup tabs:', error);
    });
  }, []);



  // StyledApp 样式 - Tailwind 版本
  // 为轻量级AI工具栏预留左侧空间
  const isMacOS = navigator.platform.toLowerCase().includes('mac');
  const macOSControlButtonWidth = isMacOS ? 80 : 0;
  const toolbarWidth = 64;
  const aiToolbarWidth = store.aiToolbarVisible && !store.isFullscreen ?
    (macOSControlButtonWidth + toolbarWidth) : 0;
  const appClasses = cn(
    'flex flex-col bg-white overflow-hidden'
  );

  // Line 样式 - Tailwind 版本
  const lineClasses = cn(
    'h-px w-full z-[100] relative bg-black'
  );

  const appStyle: React.CSSProperties = {
    // 在浏览器环境中，始终显示标题栏，不隐藏
    height: 'auto',
    // 为AI工具栏预留左侧空间
    marginLeft: `${aiToolbarWidth}px`,
  };

  const lineStyle: React.CSSProperties = {
    // 在浏览器环境中，不需要这个line
    height: 0,
    // 为AI工具栏预留左侧空间
    marginLeft: `${aiToolbarWidth}px`,
  };

  return (
    <>
      {/* 恢复原始App组件结构 */}
      <div
        className={appClasses}
        style={appStyle}
        onMouseOver={store.isFullscreen ? onAppEnter : undefined}
        onMouseLeave={store.isFullscreen ? onAppLeave : undefined}
      >
        <UIStyle />
        <Titlebar />
        {store.settings.object.topBarVariant === 'default' && <Toolbar />}
        <BookmarkBar />
      </div>

      <div
        className={lineClasses}
        style={lineStyle}
        onMouseOver={onLineEnter}
      />


    </>
  );
});

export default App;
