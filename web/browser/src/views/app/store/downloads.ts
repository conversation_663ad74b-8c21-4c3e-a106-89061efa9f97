import { makeObservable, observable } from 'mobx';
import { eventUtils } from '@browser/core/utils/platform-lite';

import { IDownloadItem } from '@browser/core/types';

export class DownloadsStore {
  public list: IDownloadItem[] = [];

  public constructor() {
    makeObservable(this, { list: observable });

    eventUtils.on('download-started', (e, item: IDownloadItem) => {
      this.list.push(item);

      const not = new Notification(`Downloading ${item.fileName}`, {
        body: 'Open Overlay to see the downloads.',
      });

      not.onclick = () => {
        // TODO(sentialx): downloads notification
        // store.overlay.visible = true;
      };
    });

    eventUtils.on('download-progress', (e, item: IDownloadItem) => {
      const i = this.list.find((x) => x.id === item.id);
      i.receivedBytes = item.receivedBytes;
    });

    eventUtils.on('download-completed', (e, id: string) => {
      const i = this.list.find((x) => x.id === id);
      i.completed = true;
    });
  }
}
