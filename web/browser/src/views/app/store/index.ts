import { observable, computed, action, makeObservable, runInAction } from 'mobx';
import { eventUtils } from '@browser/core/utils/platform-lite';

import { TabsStore } from './tabs';
import { SettingsStore } from './settings';
import { DownloadsStore } from './downloads';
import { ExtensionsStore } from './extensions';
import { StartupTabsStore } from './startup-tabs';
import { BookmarkBarStore } from './bookmark-bar';
import { TabGroupsStore } from './tab-groups';
import { AutoFillStore } from './autofill';
import { AddTabStore } from './add-tab';

import { getTheme } from '@browser/core/utils/themes';
import { TailwindThemeManager } from '@browser/core/utils/tailwind-theme-manager';
import { ITheme } from '@mario-ai/shared';

export interface DialogsVisibility {
  menu: boolean;
  search: boolean;
  'add-tab': boolean;
  overlay: boolean;
  'extension-popup': boolean;
  find: boolean;
  tabGroups: boolean;
  downloads: boolean;
  zoom: boolean;
  'incognito-menu': boolean;
  'downloads-dialog': boolean;
  'add-bookmark': boolean;
}

export class Store {
  public windowId: number;

  public tabs = new TabsStore();
  public settings = new SettingsStore(this);
  public downloads = new DownloadsStore();
  public extensions = new ExtensionsStore();
  public startupTabs = new StartupTabsStore(this);
  public bookmarkBar = new BookmarkBarStore(this);
  public tabGroups = new TabGroupsStore(this);
  public autofill = new AutoFillStore();
  public addTab = new AddTabStore();

  public isFullscreen = false;
  public isHTMLFullscreen = false;
  public titlebarVisible = true;
  public isCompact = false;
  public isIncognito = false;

  public navigationState = {
    canGoBack: false,
    canGoForward: false,
  };

  public dialogsVisibility: DialogsVisibility = {
    menu: false,
    search: false,
    'add-tab': false,
    overlay: false,
    'extension-popup': false,
    find: false,
    tabGroups: false,
    downloads: false,
    zoom: false,
    'incognito-menu': false,
    'downloads-dialog': false,
    'add-bookmark': false,
  };

  public barHideTimer = 0;

  public mouse = {
    x: 0,
    y: 0,
  };

  // AddressBar 相关属性
  public addressbarFocused = false;
  public addressbarEditing = false;
  public addressbarTextVisible = true;
  public addressbarValue = '';
  public addressbarUrlSegments: any[] = [];
  public inputRef: any = null;
  public mouseUpped = false;
  public zoomFactor = 1;
  public isBookmarked = false;
  public adblockBtnRef: any = null;

  // AI工具栏状态 - 轻量级实现仍需要此状态来控制内间距
  public aiToolbarVisible = true;

  public constructor() {
    // 获取真实的窗口ID
    try {
      this.windowId = eventUtils.sendSync('get-window-id') || 1;
      console.log('[Store] Window ID obtained:', this.windowId);
    } catch (error) {
      console.warn('[Store] Failed to get window ID, using default:', error);
      this.windowId = 1;
    }

    makeObservable(this, {
      isFullscreen: observable,
      isHTMLFullscreen: observable,
      titlebarVisible: observable,
      isCompact: observable,
      isIncognito: observable,
      dialogsVisibility: observable,
      mouse: observable,
      addressbarFocused: observable,
      addressbarEditing: observable,
      addressbarTextVisible: observable,
      addressbarValue: observable,
      addressbarUrlSegments: observable,
      mouseUpped: observable,
      zoomFactor: observable,
      isBookmarked: observable,
      adblockBtnRef: observable,
      aiToolbarVisible: observable, // 轻量级实现仍需要此状态来控制内间距
      theme: computed,
      updateSettings: action,
    });

    eventUtils.on('update-navigation-state', (e, navigationState) => {
      if (navigationState) {
        this.navigationState = navigationState;
      }
    });

    eventUtils.on('download-started', (e, item) => {
      this.downloads.list.push(item);
    });

    eventUtils.on('download-progress', (e, item) => {
      const i = this.downloads.list.find((x) => x.id === item.id);
      if (i) {
        i.receivedBytes = item.receivedBytes;
      }
    });

    eventUtils.on('download-completed', (e, id) => {
      const i = this.downloads.list.find((x) => x.id === id);
      if (i) {
        i.completed = true;
      }
    });

    eventUtils.on('is-bookmarked', (e, flag) => {
      if (this.tabs.selectedTab) {
        this.tabs.selectedTab.setIsBookmarked(flag);
      }
      this.isBookmarked = flag;
    });

    eventUtils.on('find', (e, tabId, ...args) => {
      this.tabs.onFind(tabId, ...args);
    });

    eventUtils.on('tab-updated', (e, tabId, changeInfo) => {
      this.tabs.onUpdated(tabId, changeInfo);
    });

    eventUtils.on('tab-favicon-updated', (e, tabId, favicon) => {
      this.tabs.onFaviconUpdated(tabId, favicon);
    });

    eventUtils.on('tab-credentials', (e, tabId, credentials) => {
      this.tabs.onCredentials(tabId, credentials);
    });

    eventUtils.on('fullscreen', (e, fullscreen) => {
      this.isFullscreen = fullscreen;
    });

    eventUtils.on('html-fullscreen', (e, fullscreen) => {
      this.isHTMLFullscreen = fullscreen;
    });

    eventUtils.on('window-focus', () => {
      this.tabs.onWindowFocus();
    });

    eventUtils.on('api-tabs-query', (e, queryInfo) => {
      this.tabs.onTabsQuery(queryInfo);
    });

    eventUtils.on('api-tabs-create', (e, createProperties) => {
      this.tabs.onTabCreate(createProperties);
    });

    eventUtils.on('api-tabs-insertCSS', (e, tabId, details) => {
      this.tabs.onTabInsertCSS(tabId, details);
    });

    eventUtils.on('load-url', (e, url, newTab) => {
      if (newTab) {
        this.tabs.addTab({ url, active: true });
      } else {
        this.tabs.selectedTab.url = url;
        this.tabs.selectedTab.callViewMethod('webContents.loadURL', url);
      }
    });

    eventUtils.on('api-browserAction-setBadgeText', (e, details) => {
      this.extensions.onBrowserActionSetBadgeText(details);
    });

    eventUtils.on('api-browserAction-getBadgeText', (e, details) => {
      this.extensions.onBrowserActionGetBadgeText(details);
    });

    eventUtils.on('api-browserAction-setPopup', (e, details) => {
      this.extensions.onBrowserActionSetPopup(details);
    });

    eventUtils.on('set-badge-text', (e, extensionId, tabId, text) => {
      this.extensions.setBadgeText(extensionId, tabId, text);
    });

    eventUtils.on('show-extension-popup', (e, left, top, url, inspect) => {
      this.extensions.showPopup(left, top, url, inspect);
    });

    eventUtils.on('hide-extension-popup', () => {
      this.extensions.hidePopup();
    });

    eventUtils.on('tabs-resize', (e, tabs) => {
      this.tabs.onResize(tabs);
    });

    eventUtils.on('blacklist', (e, item, blacklisted) => {
      this.extensions.setBlacklisted(item, blacklisted);
    });

    eventUtils.on('install-extension', (e, id) => {
      this.extensions.install(id);
    });

    eventUtils.on('show-tab-preview', (e, tab) => {
      this.tabs.onTabPreview(tab);
    });

    eventUtils.on('hide-tab-preview', () => {
      this.tabs.hideTabPreview();
    });

    eventUtils.on('zoom-factor-updated', (e, tabId, zoomFactor) => {
      this.tabs.onZoomFactorUpdated(tabId, zoomFactor);
    });

    eventUtils.on('certificate-error', (e, tabId, details) => {
      this.tabs.onCertificateError(tabId, details);
    });

    eventUtils.on('blocked-ad', (e, tabId, url) => {
      this.tabs.onBlockedAd(tabId, url);
    });

    eventUtils.on('theme-updated', () => {
      this.updateSettings({ ...this.settings.object });
    });

    eventUtils.on('incognito', (e, incognito) => {
      this.isIncognito = incognito;
    });

    eventUtils.on('compact-mode', (e, compact) => {
      this.isCompact = compact;
    });

    // 监听对话框可见性变化事件，更新按钮状态
    eventUtils.on('dialog-visibility-change', (e, dialogName, visible) => {
      console.log('[AppStore] Dialog visibility change:', dialogName, visible);
      if (this.dialogsVisibility.hasOwnProperty(dialogName)) {
        // 使用runInAction确保状态更新被正确观察
        runInAction(() => {
          this.dialogsVisibility[dialogName as keyof DialogsVisibility] = visible;
        });
      }
    });

    // 监听AI工具栏可见性变化 - 轻量级实现用于控制内间距
    eventUtils.on('ai-toolbar-visibility-changed', (e, visible) => {
      console.log('[AppStore] AI toolbar visibility change:', visible);
      runInAction(() => {
        this.aiToolbarVisible = visible;
      });
    });

    this.settings.updateSettings(this.settings.object);
  }

  public get theme(): ITheme {
    // 如果是自动模式，使用TailwindThemeManager的当前主题
    if (this.settings.object.themeAuto) {
      const currentTheme = TailwindThemeManager.getCurrentTheme();
      return getTheme(currentTheme);
    }
    // 否则使用设置中的主题
    return getTheme(this.settings.object.theme);
  }

  public updateSettings(settings: any) {
    this.settings.updateSettings(settings);
  }

  @action
  public setMouseUpped(upped: boolean) {
    this.mouseUpped = upped;
  }
}

export default new Store();