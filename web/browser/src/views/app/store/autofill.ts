import { action, makeAutoObservable } from 'mobx';
import { eventUtils } from '@browser/core/utils/platform-lite';

import { IFormFillData } from '@browser/core/types';
import { eventUtils } from '@browser/core/utils/platform-lite';

export class AutoFillStore {
  // ✅ 重构: 移除复杂的数据库抽象层，改为直接IPC调用 (借鉴原始工程)

  public credentials: IFormFillData[] = [];

  public addresses: IFormFillData[] = [];

  public menuVisible = false;

  public menuTop = 0;

  public menuLeft = 0;

  public selectedItem: IFormFillData;

  public constructor() {
    makeAutoObservable(this, { load: action, removeItem: action, db: false });

    this.load();

    eventUtils.on('credentials-insert', (e, data) => {
      this.credentials.push(data);
    });

    eventUtils.on('credentials-update', (e, data) => {
      const { _id, username, passLength } = data;
      const item = this.credentials.find((r) => r._id === _id);

      item.fields = {
        username,
        passLength,
      };
    });
  }

  public async load() {
    try {
      // ✅ 重构: 使用直接IPC调用替代复杂的数据库抽象 (借鉴原始工程)
      const items: IFormFillData[] = await eventUtils.invoke('storage-get', {
        scope: 'formfill',
        query: {}
      });

      // 添加null检查，确保items是数组
      const safeItems = items || [];
      this.credentials = safeItems.filter((r) => r.type === 'password');
      this.addresses = safeItems.filter((r) => r.type === 'address');
      console.log('[AppAutoFillStore] Loaded formfill data via IPC:', safeItems.length);
    } catch (error) {
      console.error('[AppAutoFillStore] Error loading formfill data:', error);
      this.credentials = [];
      this.addresses = [];
    }
  }

  public async removeItem(data: IFormFillData) {
    try {
      // ✅ 重构: 使用直接IPC调用替代复杂的数据库抽象 (借鉴原始工程)
      await eventUtils.invoke('storage-remove', {
        scope: 'formfill',
        query: { _id: data._id }
      });

      if (data.type === 'password') {
        this.credentials = this.credentials.filter((r) => r._id !== data._id);
      } else {
        this.addresses = this.addresses.filter((r) => r._id !== data._id);
      }
      console.log('[AppAutoFillStore] Removed formfill item via IPC:', data._id);
    } catch (error) {
      console.error('[AppAutoFillStore] Error removing formfill item:', error);
    }
  }
}
