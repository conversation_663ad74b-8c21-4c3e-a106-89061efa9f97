import * as React from 'react';
import { observer } from 'mobx-react-lite';

import store from '../../store';
import { viewUtils, eventUtils } from '@browser/core/utils/platform-lite';
import { Suggestions } from '../Suggestions';
import { ICON_SEARCH, ICON_PAGE } from '@mario-ai/shared';
import { UIStyle } from '@browser/core/styles/default-styles';
import { cn } from '@browser/utils/tailwind-helpers';
import {
  COMPACT_TITLEBAR_HEIGHT,
  DEFAULT_TITLEBAR_HEIGHT,
  TOOLBAR_HEIGHT,
} from '@mario-ai/shared';

const onKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
  if (e.which === 13) {
    // Enter.
    e.preventDefault();

    const text = e.currentTarget.value;
    let url = text;

    const suggestion = store.suggestions.selectedSuggestion;

    if (suggestion) {
      if (suggestion.isSearch) {
        url = store.searchEngine.url.replace('%s', text);
      } else if (text.indexOf('://') === -1) {
        url = `http://${text}`;
      }
    }

    e.currentTarget.value = url;

    viewUtils.callViewMethod(store.tabId, 'loadURL', url);

    store.hide({enter: true});
  }
};

const onKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
  const { suggestions } = store;
  const { list } = suggestions;
  const input = store.inputRef.current;

  store.canSuggest = store.getCanSuggest(e.keyCode);

  if (e.key === 'Escape') {
    store.hide({ focus: true, escape: true });
  } else if (e.keyCode === 38 || e.keyCode === 40) {
    e.preventDefault();
    if (
      e.keyCode === 40 &&
      suggestions.selected + 1 <= list.length - 1 + store.searchedTabs.length
    ) {
      suggestions.selected++;
    } else if (e.keyCode === 38 && suggestions.selected - 1 >= 0) {
      suggestions.selected--;
    }

    let suggestion = list.find((x) => x.id === suggestions.selected);

    if (!suggestion) {
      suggestion = store.searchedTabs.find(
        (x) => x.id === suggestions.selected,
      );
    }

    input.value = suggestion.isSearch ? suggestion.primaryText : suggestion.url;
  }
};

const onInput = (e: any) => {
  store.inputText = e.currentTarget.value;

  if (e.currentTarget.value.trim() === '') {
    store.hide({ focus: true });
  }

  // TODO: if (store.settings.object.suggestions) {
  store.suggest();
  // }
};

export const App = observer(() => {
  const suggestionsVisible = store.suggestions.list.length !== 0;

  let height = 0;

  if (suggestionsVisible) {
    for (const s of store.suggestions.list) {
      height += 38;
    }
  }

  requestAnimationFrame(() => {
    eventUtils.send(`height-${store.id}`, height);
  });

  const suggestion = store.suggestions.selectedSuggestion;
  let favicon = ICON_SEARCH;
  let customIcon = true;

  if (suggestion && suggestionsVisible) {
    favicon = suggestion.favicon;
    customIcon = false;

    if (suggestion.isSearch) {
      favicon = store.searchEngine.icon;
    } else if (
      favicon == null ||
      favicon.trim() === '' ||
      favicon === ICON_PAGE
    ) {
      favicon = ICON_PAGE;
      customIcon = true;
    }
  }

  // 计算搜索框高度
  const searchBoxHeight = store.settings.topBarVariant === 'compact'
    ? COMPACT_TITLEBAR_HEIGHT
    : TOOLBAR_HEIGHT - 1;

  // StyledApp 样式 - Tailwind 版本 (继承 DialogStyle)
  const appClasses = cn(
    // DialogStyle 基础样式
    'rounded-[10px] shadow-dialog bg-mario-searchbox pb-1',
    // 文本颜色根据主题
    store.theme['searchBox.lightForeground']
      ? 'text-[rgba(255,255,255,0.87)]'
      : 'text-black'
  );

  // SearchBox 样式
  const searchBoxClasses = cn('flex items-center');
  const searchBoxStyle: React.CSSProperties = {
    height: `${searchBoxHeight}px`,
  };

  // CurrentIcon 样式
  const currentIconClasses = cn(
    'w-4 h-4 min-w-4 ml-[11px] bg-center bg-no-repeat bg-contain'
  );

  const currentIconStyle: React.CSSProperties = {
    backgroundImage: `url(${favicon})`,
    filter: customIcon && store.theme['dialog.lightForeground']
      ? 'invert(100%)'
      : 'none',
    opacity: customIcon ? 0.54 : 1,
  };

  // Input 样式
  const inputClasses = cn(
    'outline-none border-none w-full h-full font-inherit text-sm',
    'pl-3 pr-2 pt-[1px] bg-transparent text-inherit',
    // placeholder 样式
    store.theme['searchBox.lightForeground']
      ? 'placeholder:text-[rgba(255,255,255,0.54)]'
      : 'placeholder:text-[rgba(0,0,0,0.54)]'
  );

  return (
    <div className={appClasses}>
      <UIStyle />
      <div className={searchBoxClasses} style={searchBoxStyle}>
        <div
          className={currentIconClasses}
          style={currentIconStyle}
        />
        <input
          className={inputClasses}
          onKeyDown={onKeyDown}
          onInput={onInput}
          ref={store.inputRef}
          onKeyPress={onKeyPress}
          spellCheck={false}
        />
      </div>
      <Suggestions visible={suggestionsVisible}></Suggestions>
    </div>
  );
});
