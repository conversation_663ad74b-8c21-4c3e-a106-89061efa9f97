import { observer } from 'mobx-react-lite';
import * as React from 'react';

import store from '../../store';
import { Suggestion } from '../Suggestion';
import { cn } from '@browser/utils/tailwind-helpers';

interface Props {
  visible: boolean;
}

const onMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
  e.stopPropagation();
};

export const Suggestions = observer(({ visible }: Props) => {
  // StyledSuggestions 样式 - Tailwind 版本
  const suggestionsClasses = cn(
    'w-full overflow-hidden',
    visible ? 'block' : 'hidden'
  );

  return (
    <div className={suggestionsClasses} onMouseDown={onMouseDown}>
      {store.suggestions.list.map((suggestion) => (
        <Suggestion suggestion={suggestion} key={suggestion.id} />
      ))}
    </div>
  );
});
