import { ITheme } from '@mario-ai/shared';

export class TailwindThemeManager {
  private static currentTheme: 'wexond-light' | 'wexond-dark' = 'wexond-light';

  /**
   * 设置主题
   * @param themeName 主题名称
   */
  static setTheme(themeName: 'wexond-light' | 'wexond-dark') {
    const root = document.documentElement;
    
    // 移除旧主题
    root.classList.remove('dark');
    root.removeAttribute('data-theme');
    
    // 设置新主题
    if (themeName === 'wexond-dark') {
      root.setAttribute('data-theme', 'dark');
      root.classList.add('dark');
    } else {
      root.setAttribute('data-theme', 'light');
    }
    
    this.currentTheme = themeName;
    
    // 触发主题变更事件
    window.dispatchEvent(new CustomEvent('theme-changed', { 
      detail: { theme: themeName } 
    }));
  }

  /**
   * 获取当前主题
   */
  static getCurrentTheme() {
    return this.currentTheme;
  }

  /**
   * 检查是否为深色主题
   */
  static isDarkTheme() {
    return this.currentTheme === 'wexond-dark';
  }

  /**
   * 与现有主题系统集成
   * @param existingTheme 现有主题对象
   */
  static syncWithExistingTheme(existingTheme: ITheme) {
    const isDark = existingTheme['dialog.lightForeground'];
    this.setTheme(isDark ? 'wexond-dark' : 'wexond-light');
  }

  /**
   * 从主题名称同步
   * @param themeName 主题名称字符串
   */
  static syncFromThemeName(themeName: string) {
    if (themeName === 'wexond-dark') {
      this.setTheme('wexond-dark');
    } else {
      this.setTheme('wexond-light');
    }
  }

  /**
   * 检测系统主题偏好
   */
  static detectSystemTheme(): 'wexond-light' | 'wexond-dark' {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'wexond-dark';
    }
    return 'wexond-light';
  }

  /**
   * 设置主题（支持自动检测）
   * @param themeName 主题名称或'auto'
   * @param isAuto 是否为自动模式
   */
  static setThemeWithAuto(themeName: string, isAuto: boolean = false) {
    if (isAuto || themeName === 'auto') {
      const systemTheme = this.detectSystemTheme();
      this.setTheme(systemTheme);
    } else {
      this.setTheme(themeName as 'wexond-light' | 'wexond-dark');
    }

    // 通知轻量级AI工具栏更新主题
    this.notifyAIToolbarThemeChange(this.currentTheme);
  }

  /**
   * 通知AI工具栏主题变化
   * @param theme 主题名称
   */
  private static notifyAIToolbarThemeChange(theme: string) {
    try {
      const { ipcRenderer } = require('electron');
      const windowId = (window as any).windowId || 1;
      ipcRenderer.send(`ai-toolbar-theme-change-${windowId}`, theme);
      console.log('[TailwindThemeManager] Notified AI toolbar of theme change:', theme);
    } catch (error) {
      console.warn('[TailwindThemeManager] Failed to notify AI toolbar theme change:', error);
    }
  }

  /**
   * 初始化主题系统
   * @param initialTheme 初始主题
   */
  static initialize(initialTheme?: string | ITheme) {
    if (typeof initialTheme === 'string') {
      this.syncFromThemeName(initialTheme);
    } else if (initialTheme && typeof initialTheme === 'object') {
      this.syncWithExistingTheme(initialTheme);
    } else {
      // 默认使用浅色主题
      this.setTheme('wexond-light');
    }
  }
}
