import * as React from 'react';
import { cn } from '@browser/utils/tailwind-helpers';

// Container 组件
interface ContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  darken?: boolean;
  children?: React.ReactNode;
  className?: string;
}

export const Container = React.forwardRef<HTMLDivElement, ContainerProps>(
  ({ darken = false, children, className, ...props }, ref) => {
    const classes = cn(
      // 原始样式：display: flex; overflow: auto; height: 100vh; overflow: hidden;
      'flex overflow-auto h-screen overflow-hidden',
      // 添加页面背景色
      'bg-mario-page text-mario-page-text',
      // 伪元素遮罩层
      'after:content-[""] after:fixed after:z-[99] after:left-0 after:right-0 after:top-0 after:bottom-0',
      'after:bg-black after:transition-opacity after:duration-200',
      darken
        ? 'after:opacity-54 after:pointer-events-auto'
        : 'after:opacity-0 after:pointer-events-none',
      className
    );

    return (
      <div ref={ref} className={classes} {...props}>
        {children}
      </div>
    );
  }
);

Container.displayName = 'Container';

// Content 组件
interface ContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode;
  className?: string;
}

export const Content = React.forwardRef<HTMLDivElement, ContentProps>(
  ({ children, className, ...props }, ref) => {
    const classes = cn(
      // 原始样式：height: 100vh; flex: 1; overflow: auto;
      'h-screen flex-1 overflow-auto',
      className
    );

    return (
      <div ref={ref} className={classes} {...props}>
        {children}
      </div>
    );
  }
);

Content.displayName = 'Content';

// LeftContent 组件
interface LeftContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode;
  className?: string;
}

export const LeftContent = React.forwardRef<HTMLDivElement, LeftContentProps>(
  ({ children, className, style, ...props }, ref) => {
    const classes = cn(
      // 原始样式：position: relative; margin: 64px; max-width: 1024px;
      'relative max-w-[1024px]',
      className
    );

    // 原始样式完全对应：margin: 64px
    const defaultStyle = {
      margin: '64px',
      ...style
    };

    return (
      <div ref={ref} className={classes} style={defaultStyle} {...props}>
        {children}
      </div>
    );
  }
);

LeftContent.displayName = 'LeftContent';
