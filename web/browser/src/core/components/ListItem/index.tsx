import * as React from 'react';
import { cn } from '@browser/utils/tailwind-helpers';

interface ListItemProps extends React.HTMLAttributes<HTMLDivElement> {
  selected?: boolean;
  className?: string;
  children?: React.ReactNode;
}

export const ListItem = React.forwardRef<HTMLDivElement, ListItemProps>(
  ({ selected = false, className, children, ...props }, ref) => {
    const classes = cn(
      // 基础样式
      'flex items-center px-6 h-12 overflow-hidden',
      // 文本颜色 - 根据主题
      'text-mario-page-text',
      // 背景色 - 根据选中状态
      selected 
        ? 'bg-black bg-opacity-8 dark:bg-white dark:bg-opacity-12'
        : 'bg-transparent',
      // Hover 效果
      selected
        ? 'hover:bg-black hover:bg-opacity-8 dark:hover:bg-white dark:hover:bg-opacity-12'
        : 'hover:bg-black hover:bg-opacity-4 dark:hover:bg-white dark:hover:bg-opacity-8',
      className
    );

    return (
      <div ref={ref} className={classes} {...props}>
        {children}
      </div>
    );
  }
);

ListItem.displayName = 'ListItem';
