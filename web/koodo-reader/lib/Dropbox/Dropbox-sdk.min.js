!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).Dropbox={})}(this,(function(e){"use strict";function t(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function r(e,t){for(var r=0;t.length>r;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}function s(e,t,s){return t&&r(e.prototype,t),s&&r(e,s),e}function i(e){return(i=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function n(e,t){return(n=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function u(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function a(e,t,r){return(a=u()?Reflect.construct:function(e,t,r){var s=[null];s.push.apply(s,t);var i=new(Function.bind.apply(e,s));return r&&n(i,r.prototype),i}).apply(null,arguments)}function o(e){var t="function"==typeof Map?new Map:void 0;return(o=function(e){if(null===e||-1===Function.toString.call(e).indexOf("[native code]"))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return a(e,arguments,i(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),n(r,e)})(e)}function c(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}var p="app",l="user",h="team",f="dropboxapi.com",m="dropbox.com",d={api:"api",notify:"bolt",content:"api-content"},_={};function q(e){var t="000".concat(e.charCodeAt(0).toString(16)).slice(-4);return"\\u".concat(t)}_.accountSetProfilePhoto=function(e){return this.request("account/set_profile_photo",e,"user","api","rpc")},_.authTokenFromOauth1=function(e){return this.request("auth/token/from_oauth1",e,"app","api","rpc")},_.authTokenRevoke=function(){return this.request("auth/token/revoke",null,"user","api","rpc")},_.checkApp=function(e){return this.request("check/app",e,"app","api","rpc")},_.checkUser=function(e){return this.request("check/user",e,"user","api","rpc")},_.contactsDeleteManualContacts=function(){return this.request("contacts/delete_manual_contacts",null,"user","api","rpc")},_.contactsDeleteManualContactsBatch=function(e){return this.request("contacts/delete_manual_contacts_batch",e,"user","api","rpc")},_.filePropertiesPropertiesAdd=function(e){return this.request("file_properties/properties/add",e,"user","api","rpc")},_.filePropertiesPropertiesOverwrite=function(e){return this.request("file_properties/properties/overwrite",e,"user","api","rpc")},_.filePropertiesPropertiesRemove=function(e){return this.request("file_properties/properties/remove",e,"user","api","rpc")},_.filePropertiesPropertiesSearch=function(e){return this.request("file_properties/properties/search",e,"user","api","rpc")},_.filePropertiesPropertiesSearchContinue=function(e){return this.request("file_properties/properties/search/continue",e,"user","api","rpc")},_.filePropertiesPropertiesUpdate=function(e){return this.request("file_properties/properties/update",e,"user","api","rpc")},_.filePropertiesTemplatesAddForTeam=function(e){return this.request("file_properties/templates/add_for_team",e,"team","api","rpc")},_.filePropertiesTemplatesAddForUser=function(e){return this.request("file_properties/templates/add_for_user",e,"user","api","rpc")},_.filePropertiesTemplatesGetForTeam=function(e){return this.request("file_properties/templates/get_for_team",e,"team","api","rpc")},_.filePropertiesTemplatesGetForUser=function(e){return this.request("file_properties/templates/get_for_user",e,"user","api","rpc")},_.filePropertiesTemplatesListForTeam=function(){return this.request("file_properties/templates/list_for_team",null,"team","api","rpc")},_.filePropertiesTemplatesListForUser=function(){return this.request("file_properties/templates/list_for_user",null,"user","api","rpc")},_.filePropertiesTemplatesRemoveForTeam=function(e){return this.request("file_properties/templates/remove_for_team",e,"team","api","rpc")},_.filePropertiesTemplatesRemoveForUser=function(e){return this.request("file_properties/templates/remove_for_user",e,"user","api","rpc")},_.filePropertiesTemplatesUpdateForTeam=function(e){return this.request("file_properties/templates/update_for_team",e,"team","api","rpc")},_.filePropertiesTemplatesUpdateForUser=function(e){return this.request("file_properties/templates/update_for_user",e,"user","api","rpc")},_.fileRequestsCount=function(){return this.request("file_requests/count",null,"user","api","rpc")},_.fileRequestsCreate=function(e){return this.request("file_requests/create",e,"user","api","rpc")},_.fileRequestsDelete=function(e){return this.request("file_requests/delete",e,"user","api","rpc")},_.fileRequestsDeleteAllClosed=function(){return this.request("file_requests/delete_all_closed",null,"user","api","rpc")},_.fileRequestsGet=function(e){return this.request("file_requests/get",e,"user","api","rpc")},_.fileRequestsListV2=function(e){return this.request("file_requests/list_v2",e,"user","api","rpc")},_.fileRequestsList=function(){return this.request("file_requests/list",null,"user","api","rpc")},_.fileRequestsListContinue=function(e){return this.request("file_requests/list/continue",e,"user","api","rpc")},_.fileRequestsUpdate=function(e){return this.request("file_requests/update",e,"user","api","rpc")},_.filesAlphaGetMetadata=function(e){return this.request("files/alpha/get_metadata",e,"user","api","rpc")},_.filesAlphaUpload=function(e){return this.request("files/alpha/upload",e,"user","content","upload")},_.filesCopyV2=function(e){return this.request("files/copy_v2",e,"user","api","rpc")},_.filesCopy=function(e){return this.request("files/copy",e,"user","api","rpc")},_.filesCopyBatchV2=function(e){return this.request("files/copy_batch_v2",e,"user","api","rpc")},_.filesCopyBatch=function(e){return this.request("files/copy_batch",e,"user","api","rpc")},_.filesCopyBatchCheckV2=function(e){return this.request("files/copy_batch/check_v2",e,"user","api","rpc")},_.filesCopyBatchCheck=function(e){return this.request("files/copy_batch/check",e,"user","api","rpc")},_.filesCopyReferenceGet=function(e){return this.request("files/copy_reference/get",e,"user","api","rpc")},_.filesCopyReferenceSave=function(e){return this.request("files/copy_reference/save",e,"user","api","rpc")},_.filesCreateFolderV2=function(e){return this.request("files/create_folder_v2",e,"user","api","rpc")},_.filesCreateFolder=function(e){return this.request("files/create_folder",e,"user","api","rpc")},_.filesCreateFolderBatch=function(e){return this.request("files/create_folder_batch",e,"user","api","rpc")},_.filesCreateFolderBatchCheck=function(e){return this.request("files/create_folder_batch/check",e,"user","api","rpc")},_.filesDeleteV2=function(e){return this.request("files/delete_v2",e,"user","api","rpc")},_.filesDelete=function(e){return this.request("files/delete",e,"user","api","rpc")},_.filesDeleteBatch=function(e){return this.request("files/delete_batch",e,"user","api","rpc")},_.filesDeleteBatchCheck=function(e){return this.request("files/delete_batch/check",e,"user","api","rpc")},_.filesDownload=function(e){return this.request("files/download",e,"user","content","download")},_.filesDownloadZip=function(e){return this.request("files/download_zip",e,"user","content","download")},_.filesExport=function(e){return this.request("files/export",e,"user","content","download")},_.filesGetFileLockBatch=function(e){return this.request("files/get_file_lock_batch",e,"user","api","rpc")},_.filesGetMetadata=function(e){return this.request("files/get_metadata",e,"user","api","rpc")},_.filesGetPreview=function(e){return this.request("files/get_preview",e,"user","content","download")},_.filesGetTemporaryLink=function(e){return this.request("files/get_temporary_link",e,"user","api","rpc")},_.filesGetTemporaryUploadLink=function(e){return this.request("files/get_temporary_upload_link",e,"user","api","rpc")},_.filesGetThumbnail=function(e){return this.request("files/get_thumbnail",e,"user","content","download")},_.filesGetThumbnailV2=function(e){return this.request("files/get_thumbnail_v2",e,"app, user","content","download")},_.filesGetThumbnailBatch=function(e){return this.request("files/get_thumbnail_batch",e,"user","content","rpc")},_.filesListFolder=function(e){return this.request("files/list_folder",e,"user","api","rpc")},_.filesListFolderContinue=function(e){return this.request("files/list_folder/continue",e,"user","api","rpc")},_.filesListFolderGetLatestCursor=function(e){return this.request("files/list_folder/get_latest_cursor",e,"user","api","rpc")},_.filesListFolderLongpoll=function(e){return this.request("files/list_folder/longpoll",e,"noauth","notify","rpc")},_.filesListRevisions=function(e){return this.request("files/list_revisions",e,"user","api","rpc")},_.filesLockFileBatch=function(e){return this.request("files/lock_file_batch",e,"user","api","rpc")},_.filesMoveV2=function(e){return this.request("files/move_v2",e,"user","api","rpc")},_.filesMove=function(e){return this.request("files/move",e,"user","api","rpc")},_.filesMoveBatchV2=function(e){return this.request("files/move_batch_v2",e,"user","api","rpc")},_.filesMoveBatch=function(e){return this.request("files/move_batch",e,"user","api","rpc")},_.filesMoveBatchCheckV2=function(e){return this.request("files/move_batch/check_v2",e,"user","api","rpc")},_.filesMoveBatchCheck=function(e){return this.request("files/move_batch/check",e,"user","api","rpc")},_.filesPaperCreate=function(e){return this.request("files/paper/create",e,"user","api","upload")},_.filesPaperUpdate=function(e){return this.request("files/paper/update",e,"user","api","upload")},_.filesPermanentlyDelete=function(e){return this.request("files/permanently_delete",e,"user","api","rpc")},_.filesPropertiesAdd=function(e){return this.request("files/properties/add",e,"user","api","rpc")},_.filesPropertiesOverwrite=function(e){return this.request("files/properties/overwrite",e,"user","api","rpc")},_.filesPropertiesRemove=function(e){return this.request("files/properties/remove",e,"user","api","rpc")},_.filesPropertiesTemplateGet=function(e){return this.request("files/properties/template/get",e,"user","api","rpc")},_.filesPropertiesTemplateList=function(){return this.request("files/properties/template/list",null,"user","api","rpc")},_.filesPropertiesUpdate=function(e){return this.request("files/properties/update",e,"user","api","rpc")},_.filesRestore=function(e){return this.request("files/restore",e,"user","api","rpc")},_.filesSaveUrl=function(e){return this.request("files/save_url",e,"user","api","rpc")},_.filesSaveUrlCheckJobStatus=function(e){return this.request("files/save_url/check_job_status",e,"user","api","rpc")},_.filesSearch=function(e){return this.request("files/search",e,"user","api","rpc")},_.filesSearchV2=function(e){return this.request("files/search_v2",e,"user","api","rpc")},_.filesSearchContinueV2=function(e){return this.request("files/search/continue_v2",e,"user","api","rpc")},_.filesUnlockFileBatch=function(e){return this.request("files/unlock_file_batch",e,"user","api","rpc")},_.filesUpload=function(e){return this.request("files/upload",e,"user","content","upload")},_.filesUploadSessionAppendV2=function(e){return this.request("files/upload_session/append_v2",e,"user","content","upload")},_.filesUploadSessionAppend=function(e){return this.request("files/upload_session/append",e,"user","content","upload")},_.filesUploadSessionFinish=function(e){return this.request("files/upload_session/finish",e,"user","content","upload")},_.filesUploadSessionFinishBatch=function(e){return this.request("files/upload_session/finish_batch",e,"user","api","rpc")},_.filesUploadSessionFinishBatchCheck=function(e){return this.request("files/upload_session/finish_batch/check",e,"user","api","rpc")},_.filesUploadSessionStart=function(e){return this.request("files/upload_session/start",e,"user","content","upload")},_.paperDocsArchive=function(e){return this.request("paper/docs/archive",e,"user","api","rpc")},_.paperDocsCreate=function(e){return this.request("paper/docs/create",e,"user","api","upload")},_.paperDocsDownload=function(e){return this.request("paper/docs/download",e,"user","api","download")},_.paperDocsFolderUsersList=function(e){return this.request("paper/docs/folder_users/list",e,"user","api","rpc")},_.paperDocsFolderUsersListContinue=function(e){return this.request("paper/docs/folder_users/list/continue",e,"user","api","rpc")},_.paperDocsGetFolderInfo=function(e){return this.request("paper/docs/get_folder_info",e,"user","api","rpc")},_.paperDocsList=function(e){return this.request("paper/docs/list",e,"user","api","rpc")},_.paperDocsListContinue=function(e){return this.request("paper/docs/list/continue",e,"user","api","rpc")},_.paperDocsPermanentlyDelete=function(e){return this.request("paper/docs/permanently_delete",e,"user","api","rpc")},_.paperDocsSharingPolicyGet=function(e){return this.request("paper/docs/sharing_policy/get",e,"user","api","rpc")},_.paperDocsSharingPolicySet=function(e){return this.request("paper/docs/sharing_policy/set",e,"user","api","rpc")},_.paperDocsUpdate=function(e){return this.request("paper/docs/update",e,"user","api","upload")},_.paperDocsUsersAdd=function(e){return this.request("paper/docs/users/add",e,"user","api","rpc")},_.paperDocsUsersList=function(e){return this.request("paper/docs/users/list",e,"user","api","rpc")},_.paperDocsUsersListContinue=function(e){return this.request("paper/docs/users/list/continue",e,"user","api","rpc")},_.paperDocsUsersRemove=function(e){return this.request("paper/docs/users/remove",e,"user","api","rpc")},_.paperFoldersCreate=function(e){return this.request("paper/folders/create",e,"user","api","rpc")},_.sharingAddFileMember=function(e){return this.request("sharing/add_file_member",e,"user","api","rpc")},_.sharingAddFolderMember=function(e){return this.request("sharing/add_folder_member",e,"user","api","rpc")},_.sharingChangeFileMemberAccess=function(e){return this.request("sharing/change_file_member_access",e,"user","api","rpc")},_.sharingCheckJobStatus=function(e){return this.request("sharing/check_job_status",e,"user","api","rpc")},_.sharingCheckRemoveMemberJobStatus=function(e){return this.request("sharing/check_remove_member_job_status",e,"user","api","rpc")},_.sharingCheckShareJobStatus=function(e){return this.request("sharing/check_share_job_status",e,"user","api","rpc")},_.sharingCreateSharedLink=function(e){return this.request("sharing/create_shared_link",e,"user","api","rpc")},_.sharingCreateSharedLinkWithSettings=function(e){return this.request("sharing/create_shared_link_with_settings",e,"user","api","rpc")},_.sharingGetFileMetadata=function(e){return this.request("sharing/get_file_metadata",e,"user","api","rpc")},_.sharingGetFileMetadataBatch=function(e){return this.request("sharing/get_file_metadata/batch",e,"user","api","rpc")},_.sharingGetFolderMetadata=function(e){return this.request("sharing/get_folder_metadata",e,"user","api","rpc")},_.sharingGetSharedLinkFile=function(e){return this.request("sharing/get_shared_link_file",e,"user","content","download")},_.sharingGetSharedLinkMetadata=function(e){return this.request("sharing/get_shared_link_metadata",e,"user","api","rpc")},_.sharingGetSharedLinks=function(e){return this.request("sharing/get_shared_links",e,"user","api","rpc")},_.sharingListFileMembers=function(e){return this.request("sharing/list_file_members",e,"user","api","rpc")},_.sharingListFileMembersBatch=function(e){return this.request("sharing/list_file_members/batch",e,"user","api","rpc")},_.sharingListFileMembersContinue=function(e){return this.request("sharing/list_file_members/continue",e,"user","api","rpc")},_.sharingListFolderMembers=function(e){return this.request("sharing/list_folder_members",e,"user","api","rpc")},_.sharingListFolderMembersContinue=function(e){return this.request("sharing/list_folder_members/continue",e,"user","api","rpc")},_.sharingListFolders=function(e){return this.request("sharing/list_folders",e,"user","api","rpc")},_.sharingListFoldersContinue=function(e){return this.request("sharing/list_folders/continue",e,"user","api","rpc")},_.sharingListMountableFolders=function(e){return this.request("sharing/list_mountable_folders",e,"user","api","rpc")},_.sharingListMountableFoldersContinue=function(e){return this.request("sharing/list_mountable_folders/continue",e,"user","api","rpc")},_.sharingListReceivedFiles=function(e){return this.request("sharing/list_received_files",e,"user","api","rpc")},_.sharingListReceivedFilesContinue=function(e){return this.request("sharing/list_received_files/continue",e,"user","api","rpc")},_.sharingListSharedLinks=function(e){return this.request("sharing/list_shared_links",e,"user","api","rpc")},_.sharingModifySharedLinkSettings=function(e){return this.request("sharing/modify_shared_link_settings",e,"user","api","rpc")},_.sharingMountFolder=function(e){return this.request("sharing/mount_folder",e,"user","api","rpc")},_.sharingRelinquishFileMembership=function(e){return this.request("sharing/relinquish_file_membership",e,"user","api","rpc")},_.sharingRelinquishFolderMembership=function(e){return this.request("sharing/relinquish_folder_membership",e,"user","api","rpc")},_.sharingRemoveFileMember=function(e){return this.request("sharing/remove_file_member",e,"user","api","rpc")},_.sharingRemoveFileMember2=function(e){return this.request("sharing/remove_file_member_2",e,"user","api","rpc")},_.sharingRemoveFolderMember=function(e){return this.request("sharing/remove_folder_member",e,"user","api","rpc")},_.sharingRevokeSharedLink=function(e){return this.request("sharing/revoke_shared_link",e,"user","api","rpc")},_.sharingSetAccessInheritance=function(e){return this.request("sharing/set_access_inheritance",e,"user","api","rpc")},_.sharingShareFolder=function(e){return this.request("sharing/share_folder",e,"user","api","rpc")},_.sharingTransferFolder=function(e){return this.request("sharing/transfer_folder",e,"user","api","rpc")},_.sharingUnmountFolder=function(e){return this.request("sharing/unmount_folder",e,"user","api","rpc")},_.sharingUnshareFile=function(e){return this.request("sharing/unshare_file",e,"user","api","rpc")},_.sharingUnshareFolder=function(e){return this.request("sharing/unshare_folder",e,"user","api","rpc")},_.sharingUpdateFileMember=function(e){return this.request("sharing/update_file_member",e,"user","api","rpc")},_.sharingUpdateFolderMember=function(e){return this.request("sharing/update_folder_member",e,"user","api","rpc")},_.sharingUpdateFolderPolicy=function(e){return this.request("sharing/update_folder_policy",e,"user","api","rpc")},_.teamDevicesListMemberDevices=function(e){return this.request("team/devices/list_member_devices",e,"team","api","rpc")},_.teamDevicesListMembersDevices=function(e){return this.request("team/devices/list_members_devices",e,"team","api","rpc")},_.teamDevicesListTeamDevices=function(e){return this.request("team/devices/list_team_devices",e,"team","api","rpc")},_.teamDevicesRevokeDeviceSession=function(e){return this.request("team/devices/revoke_device_session",e,"team","api","rpc")},_.teamDevicesRevokeDeviceSessionBatch=function(e){return this.request("team/devices/revoke_device_session_batch",e,"team","api","rpc")},_.teamFeaturesGetValues=function(e){return this.request("team/features/get_values",e,"team","api","rpc")},_.teamGetInfo=function(){return this.request("team/get_info",null,"team","api","rpc")},_.teamGroupsCreate=function(e){return this.request("team/groups/create",e,"team","api","rpc")},_.teamGroupsDelete=function(e){return this.request("team/groups/delete",e,"team","api","rpc")},_.teamGroupsGetInfo=function(e){return this.request("team/groups/get_info",e,"team","api","rpc")},_.teamGroupsJobStatusGet=function(e){return this.request("team/groups/job_status/get",e,"team","api","rpc")},_.teamGroupsList=function(e){return this.request("team/groups/list",e,"team","api","rpc")},_.teamGroupsListContinue=function(e){return this.request("team/groups/list/continue",e,"team","api","rpc")},_.teamGroupsMembersAdd=function(e){return this.request("team/groups/members/add",e,"team","api","rpc")},_.teamGroupsMembersList=function(e){return this.request("team/groups/members/list",e,"team","api","rpc")},_.teamGroupsMembersListContinue=function(e){return this.request("team/groups/members/list/continue",e,"team","api","rpc")},_.teamGroupsMembersRemove=function(e){return this.request("team/groups/members/remove",e,"team","api","rpc")},_.teamGroupsMembersSetAccessType=function(e){return this.request("team/groups/members/set_access_type",e,"team","api","rpc")},_.teamGroupsUpdate=function(e){return this.request("team/groups/update",e,"team","api","rpc")},_.teamLegalHoldsCreatePolicy=function(e){return this.request("team/legal_holds/create_policy",e,"team","api","rpc")},_.teamLegalHoldsGetPolicy=function(e){return this.request("team/legal_holds/get_policy",e,"team","api","rpc")},_.teamLegalHoldsListHeldRevisions=function(e){return this.request("team/legal_holds/list_held_revisions",e,"team","api","rpc")},_.teamLegalHoldsListHeldRevisionsContinue=function(e){return this.request("team/legal_holds/list_held_revisions_continue",e,"team","api","rpc")},_.teamLegalHoldsListPolicies=function(e){return this.request("team/legal_holds/list_policies",e,"team","api","rpc")},_.teamLegalHoldsReleasePolicy=function(e){return this.request("team/legal_holds/release_policy",e,"team","api","rpc")},_.teamLegalHoldsUpdatePolicy=function(e){return this.request("team/legal_holds/update_policy",e,"team","api","rpc")},_.teamLinkedAppsListMemberLinkedApps=function(e){return this.request("team/linked_apps/list_member_linked_apps",e,"team","api","rpc")},_.teamLinkedAppsListMembersLinkedApps=function(e){return this.request("team/linked_apps/list_members_linked_apps",e,"team","api","rpc")},_.teamLinkedAppsListTeamLinkedApps=function(e){return this.request("team/linked_apps/list_team_linked_apps",e,"team","api","rpc")},_.teamLinkedAppsRevokeLinkedApp=function(e){return this.request("team/linked_apps/revoke_linked_app",e,"team","api","rpc")},_.teamLinkedAppsRevokeLinkedAppBatch=function(e){return this.request("team/linked_apps/revoke_linked_app_batch",e,"team","api","rpc")},_.teamMemberSpaceLimitsExcludedUsersAdd=function(e){return this.request("team/member_space_limits/excluded_users/add",e,"team","api","rpc")},_.teamMemberSpaceLimitsExcludedUsersList=function(e){return this.request("team/member_space_limits/excluded_users/list",e,"team","api","rpc")},_.teamMemberSpaceLimitsExcludedUsersListContinue=function(e){return this.request("team/member_space_limits/excluded_users/list/continue",e,"team","api","rpc")},_.teamMemberSpaceLimitsExcludedUsersRemove=function(e){return this.request("team/member_space_limits/excluded_users/remove",e,"team","api","rpc")},_.teamMemberSpaceLimitsGetCustomQuota=function(e){return this.request("team/member_space_limits/get_custom_quota",e,"team","api","rpc")},_.teamMemberSpaceLimitsRemoveCustomQuota=function(e){return this.request("team/member_space_limits/remove_custom_quota",e,"team","api","rpc")},_.teamMemberSpaceLimitsSetCustomQuota=function(e){return this.request("team/member_space_limits/set_custom_quota",e,"team","api","rpc")},_.teamMembersAddV2=function(e){return this.request("team/members/add_v2",e,"team","api","rpc")},_.teamMembersAdd=function(e){return this.request("team/members/add",e,"team","api","rpc")},_.teamMembersAddJobStatusGetV2=function(e){return this.request("team/members/add/job_status/get_v2",e,"team","api","rpc")},_.teamMembersAddJobStatusGet=function(e){return this.request("team/members/add/job_status/get",e,"team","api","rpc")},_.teamMembersDeleteProfilePhotoV2=function(e){return this.request("team/members/delete_profile_photo_v2",e,"team","api","rpc")},_.teamMembersDeleteProfilePhoto=function(e){return this.request("team/members/delete_profile_photo",e,"team","api","rpc")},_.teamMembersGetAvailableTeamMemberRoles=function(){return this.request("team/members/get_available_team_member_roles",null,"team","api","rpc")},_.teamMembersGetInfoV2=function(e){return this.request("team/members/get_info_v2",e,"team","api","rpc")},_.teamMembersGetInfo=function(e){return this.request("team/members/get_info",e,"team","api","rpc")},_.teamMembersListV2=function(e){return this.request("team/members/list_v2",e,"team","api","rpc")},_.teamMembersList=function(e){return this.request("team/members/list",e,"team","api","rpc")},_.teamMembersListContinueV2=function(e){return this.request("team/members/list/continue_v2",e,"team","api","rpc")},_.teamMembersListContinue=function(e){return this.request("team/members/list/continue",e,"team","api","rpc")},_.teamMembersMoveFormerMemberFiles=function(e){return this.request("team/members/move_former_member_files",e,"team","api","rpc")},_.teamMembersMoveFormerMemberFilesJobStatusCheck=function(e){return this.request("team/members/move_former_member_files/job_status/check",e,"team","api","rpc")},_.teamMembersRecover=function(e){return this.request("team/members/recover",e,"team","api","rpc")},_.teamMembersRemove=function(e){return this.request("team/members/remove",e,"team","api","rpc")},_.teamMembersRemoveJobStatusGet=function(e){return this.request("team/members/remove/job_status/get",e,"team","api","rpc")},_.teamMembersSecondaryEmailsAdd=function(e){return this.request("team/members/secondary_emails/add",e,"team","api","rpc")},_.teamMembersSecondaryEmailsDelete=function(e){return this.request("team/members/secondary_emails/delete",e,"team","api","rpc")},_.teamMembersSecondaryEmailsResendVerificationEmails=function(e){return this.request("team/members/secondary_emails/resend_verification_emails",e,"team","api","rpc")},_.teamMembersSendWelcomeEmail=function(e){return this.request("team/members/send_welcome_email",e,"team","api","rpc")},_.teamMembersSetAdminPermissionsV2=function(e){return this.request("team/members/set_admin_permissions_v2",e,"team","api","rpc")},_.teamMembersSetAdminPermissions=function(e){return this.request("team/members/set_admin_permissions",e,"team","api","rpc")},_.teamMembersSetProfileV2=function(e){return this.request("team/members/set_profile_v2",e,"team","api","rpc")},_.teamMembersSetProfile=function(e){return this.request("team/members/set_profile",e,"team","api","rpc")},_.teamMembersSetProfilePhotoV2=function(e){return this.request("team/members/set_profile_photo_v2",e,"team","api","rpc")},_.teamMembersSetProfilePhoto=function(e){return this.request("team/members/set_profile_photo",e,"team","api","rpc")},_.teamMembersSuspend=function(e){return this.request("team/members/suspend",e,"team","api","rpc")},_.teamMembersUnsuspend=function(e){return this.request("team/members/unsuspend",e,"team","api","rpc")},_.teamNamespacesList=function(e){return this.request("team/namespaces/list",e,"team","api","rpc")},_.teamNamespacesListContinue=function(e){return this.request("team/namespaces/list/continue",e,"team","api","rpc")},_.teamPropertiesTemplateAdd=function(e){return this.request("team/properties/template/add",e,"team","api","rpc")},_.teamPropertiesTemplateGet=function(e){return this.request("team/properties/template/get",e,"team","api","rpc")},_.teamPropertiesTemplateList=function(){return this.request("team/properties/template/list",null,"team","api","rpc")},_.teamPropertiesTemplateUpdate=function(e){return this.request("team/properties/template/update",e,"team","api","rpc")},_.teamReportsGetActivity=function(e){return this.request("team/reports/get_activity",e,"team","api","rpc")},_.teamReportsGetDevices=function(e){return this.request("team/reports/get_devices",e,"team","api","rpc")},_.teamReportsGetMembership=function(e){return this.request("team/reports/get_membership",e,"team","api","rpc")},_.teamReportsGetStorage=function(e){return this.request("team/reports/get_storage",e,"team","api","rpc")},_.teamTeamFolderActivate=function(e){return this.request("team/team_folder/activate",e,"team","api","rpc")},_.teamTeamFolderArchive=function(e){return this.request("team/team_folder/archive",e,"team","api","rpc")},_.teamTeamFolderArchiveCheck=function(e){return this.request("team/team_folder/archive/check",e,"team","api","rpc")},_.teamTeamFolderCreate=function(e){return this.request("team/team_folder/create",e,"team","api","rpc")},_.teamTeamFolderGetInfo=function(e){return this.request("team/team_folder/get_info",e,"team","api","rpc")},_.teamTeamFolderList=function(e){return this.request("team/team_folder/list",e,"team","api","rpc")},_.teamTeamFolderListContinue=function(e){return this.request("team/team_folder/list/continue",e,"team","api","rpc")},_.teamTeamFolderPermanentlyDelete=function(e){return this.request("team/team_folder/permanently_delete",e,"team","api","rpc")},_.teamTeamFolderRename=function(e){return this.request("team/team_folder/rename",e,"team","api","rpc")},_.teamTeamFolderUpdateSyncSettings=function(e){return this.request("team/team_folder/update_sync_settings",e,"team","api","rpc")},_.teamTokenGetAuthenticatedAdmin=function(){return this.request("team/token/get_authenticated_admin",null,"team","api","rpc")},_.teamLogGetEvents=function(e){return this.request("team_log/get_events",e,"team","api","rpc")},_.teamLogGetEventsContinue=function(e){return this.request("team_log/get_events/continue",e,"team","api","rpc")},_.usersFeaturesGetValues=function(e){return this.request("users/features/get_values",e,"user","api","rpc")},_.usersGetAccount=function(e){return this.request("users/get_account",e,"user","api","rpc")},_.usersGetAccountBatch=function(e){return this.request("users/get_account_batch",e,"user","api","rpc")},_.usersGetCurrentAccount=function(){return this.request("users/get_current_account",null,"user","api","rpc")},_.usersGetSpaceUsage=function(){return this.request("users/get_space_usage",null,"user","api","rpc")};var g=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:f,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".";return t!==f&&void 0!==d[e]&&(e=d[e],r="-"),"https://".concat(e).concat(r).concat(t,"/2/")},b=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m;return e!==m&&(e="meta-".concat(e)),"https://".concat(e,"/oauth2/authorize")},v=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:".",r="api";return e!==f&&(r=d[r],t="-"),"https://".concat(r).concat(t).concat(e,"/oauth2/token")};function k(e){return JSON.stringify(e).replace(/[\u007f-\uffff]/g,q)}function y(e){return new Date(Date.now()+1e3*e)}function A(){return"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope||"undefined"==typeof module||"undefined"!=typeof window}function C(){return"undefined"!=typeof window}function S(e){return e.toString("base64").replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}var T,L,w,M=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&n(e,t)}(o,e);var r,s,a=(r=o,s=u(),function(){var e,t=i(r);if(s){var n=i(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return c(this,e)});function o(e,r,s){var i;return t(this,o),(i=a.call(this,"Response failed with a ".concat(e," code"))).name="DropboxResponseError",i.status=e,i.headers=r,i.error=s,i}return o}(o(Error)),P=function e(r,s,i){t(this,e),this.status=r,this.headers=s,this.result=i};function F(e){return e.text().then((function(t){var r;try{r=JSON.parse(t)}catch(e){r=t}throw new M(e.status,e.headers,r)}))}function R(e){return e.ok?e.text().then((function(t){var r;try{r=JSON.parse(t)}catch(e){r=t}return new P(e.status,e.headers,r)})):F(e)}T=C()?window.fetch.bind(window):require("node-fetch"),L=C()?window.crypto||window.msCrypto:require("crypto"),w="undefined"==typeof TextEncoder?require("util").TextEncoder:TextEncoder;var D,G=["legacy","offline","online"],U=["code","token"],E=["none","user","team"],x=function(){function e(r){t(this,e),this.fetch=(r=r||{}).fetch||T,this.accessToken=r.accessToken,this.accessTokenExpiresAt=r.accessTokenExpiresAt,this.refreshToken=r.refreshToken,this.clientId=r.clientId,this.clientSecret=r.clientSecret,this.domain=r.domain,this.domainDelimiter=r.domainDelimiter}return s(e,[{key:"setAccessToken",value:function(e){this.accessToken=e}},{key:"getAccessToken",value:function(){return this.accessToken}},{key:"setClientId",value:function(e){this.clientId=e}},{key:"getClientId",value:function(){return this.clientId}},{key:"setClientSecret",value:function(e){this.clientSecret=e}},{key:"getClientSecret",value:function(){return this.clientSecret}},{key:"getRefreshToken",value:function(){return this.refreshToken}},{key:"setRefreshToken",value:function(e){this.refreshToken=e}},{key:"getAccessTokenExpiresAt",value:function(){return this.accessTokenExpiresAt}},{key:"setAccessTokenExpiresAt",value:function(e){this.accessTokenExpiresAt=e}},{key:"setCodeVerifier",value:function(e){this.codeVerifier=e}},{key:"getCodeVerifier",value:function(){return this.codeVerifier}},{key:"generateCodeChallenge",value:function(){var e,t=this,r=(new w).encode(this.codeVerifier);if(C())return L.subtle.digest("SHA-256",r).then((function(r){var s=btoa(String.fromCharCode.apply(null,new Uint8Array(r)));e=S(s).substr(0,128),t.codeChallenge=e}));var s=L.createHash("sha256").update(r).digest();return e=S(s),this.codeChallenge=e,Promise.resolve()}},{key:"generatePKCECodes",value:function(){var e;if(C()){var t=new Uint8Array(128),r=L.getRandomValues(t);e=S(btoa(r)).substr(0,128)}else{e=S(L.randomBytes(128)).substr(0,128)}return this.codeVerifier=e,this.generateCodeChallenge()}},{key:"getAuthenticationUrl",value:function(e,t){var r,s=this,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"token",n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,u=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"none",o=arguments.length>6&&void 0!==arguments[6]&&arguments[6],c=this.getClientId(),p=b(this.domain);if(!c)throw Error("A client id is required. You can set the client id using .setClientId().");if("code"!==i&&!e)throw Error("A redirect uri is required.");if(!U.includes(i))throw Error("Authorization type must be code or token");if(n&&!G.includes(n))throw Error("Token Access Type must be legacy, offline, or online");if(u&&!(u instanceof Array))throw Error("Scope must be an array of strings");if(!E.includes(a))throw Error("includeGrantedScopes must be none, user, or team");return r="code"===i?"".concat(p,"?response_type=code&client_id=").concat(c):"".concat(p,"?response_type=token&client_id=").concat(c),e&&(r+="&redirect_uri=".concat(e)),t&&(r+="&state=".concat(t)),n&&(r+="&token_access_type=".concat(n)),u&&(r+="&scope=".concat(u.join(" "))),"none"!==a&&(r+="&include_granted_scopes=".concat(a)),o?this.generatePKCECodes().then((function(){return r+="&code_challenge_method=S256",r+="&code_challenge=".concat(s.codeChallenge)})):Promise.resolve(r)}},{key:"getAccessTokenFromCode",value:function(e,t){var r=this.getClientId(),s=this.getClientSecret();if(!r)throw Error("A client id is required. You can set the client id using .setClientId().");var i=v(this.domain,this.domainDelimiter);if(i+="?grant_type=authorization_code",i+="&code=".concat(t),i+="&client_id=".concat(r),s)i+="&client_secret=".concat(s);else{if(!this.codeVerifier)throw Error("You must use PKCE when generating the authorization URL to not include a client secret");i+="&code_verifier=".concat(this.codeVerifier)}e&&(i+="&redirect_uri=".concat(e));return this.fetch(i,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"}}).then((function(e){return R(e)}))}},{key:"checkAndRefreshAccessToken",value:function(){var e=this.getRefreshToken()&&this.getClientId(),t=!this.getAccessTokenExpiresAt()||new Date(Date.now()+3e5)>=this.getAccessTokenExpiresAt(),r=!this.getAccessToken();return(t||r)&&e?this.refreshAccessToken():Promise.resolve()}},{key:"refreshAccessToken",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,r=v(this.domain,this.domainDelimiter),s=this.getClientId(),i=this.getClientSecret();if(!s)throw Error("A client id is required. You can set the client id using .setClientId().");if(t&&!(t instanceof Array))throw Error("Scope must be an array of strings");var n={"Content-Type":"application/json"};r+="?grant_type=refresh_token&refresh_token=".concat(this.getRefreshToken()),r+="&client_id=".concat(s),i&&(r+="&client_secret=".concat(i)),t&&(r+="&scope=".concat(t.join(" ")));var u={method:"POST"};return u.headers=n,this.fetch(r,u).then((function(e){return R(e)})).then((function(t){e.setAccessToken(t.result.access_token),e.setAccessTokenExpiresAt(y(t.result.expires_in))}))}}]),e}();D="undefined"!=typeof window?window.fetch.bind(window):require("node-fetch");var V="undefined"==typeof btoa?function(e){return Buffer.from(e).toString("base64")}:btoa;e.Dropbox=function(){function e(r){t(this,e),this.auth=(r=r||{}).auth?r.auth:new x(r),this.fetch=r.fetch||D,this.selectUser=r.selectUser,this.selectAdmin=r.selectAdmin,this.pathRoot=r.pathRoot,this.domain=r.domain,this.domainDelimiter=r.domainDelimiter,Object.assign(this,_)}return s(e,[{key:"request",value:function(e,t,r,s,i){if(r.split(",").length>1){var n=r.replace(" ","").split(",");n.includes(l)&&this.auth.getAccessToken()?r=l:n.includes(h)&&this.auth.getAccessToken()?r=h:n.includes(p)&&(r=p)}switch(i){case"rpc":return this.rpcRequest(e,t,r,s);case"download":return this.downloadRequest(e,t,r,s);case"upload":return this.uploadRequest(e,t,r,s);default:throw Error("Invalid request style: ".concat(i))}}},{key:"rpcRequest",value:function(e,t,r,s){var i=this;return this.auth.checkAndRefreshAccessToken().then((function(){var e,s={method:"POST",body:t?JSON.stringify(t):null,headers:{}};switch(t&&(s.headers["Content-Type"]="application/json"),r){case p:if(!i.auth.clientId||!i.auth.clientSecret)throw Error("A client id and secret is required for this function");e=V("".concat(i.auth.clientId,":").concat(i.auth.clientSecret)),s.headers.Authorization="Basic ".concat(e);break;case h:case l:s.headers.Authorization="Bearer ".concat(i.auth.getAccessToken());break;case"noauth":break;default:throw Error("Unhandled auth type: ".concat(r))}return i.setCommonHeaders(s),s})).then((function(t){return i.fetch(g(s,i.domain,i.domainDelimiter)+e,t)})).then((function(e){return R(e)}))}},{key:"downloadRequest",value:function(e,t,r,s){var i=this;return this.auth.checkAndRefreshAccessToken().then((function(){if(r!==l)throw Error("Unexpected auth type: ".concat(r));var e={method:"POST",headers:{Authorization:"Bearer ".concat(i.auth.getAccessToken()),"Dropbox-API-Arg":k(t)}};return i.setCommonHeaders(e),e})).then((function(t){return i.fetch(g(s,i.domain,i.domainDelimiter)+e,t)})).then((function(e){return function(e){return e.ok?new Promise((function(t){A()?e.blob().then((function(e){return t(e)})):e.buffer().then((function(e){return t(e)}))})).then((function(t){var r=JSON.parse(e.headers.get("dropbox-api-result"));return A()?r.fileBlob=t:r.fileBinary=t,new P(e.status,e.headers,r)})):F(e)}(e)}))}},{key:"uploadRequest",value:function(e,t,r,s){var i=this;return this.auth.checkAndRefreshAccessToken().then((function(){if(r!==l)throw Error("Unexpected auth type: ".concat(r));var e=t.contents;delete t.contents;var s={body:e,method:"POST",headers:{Authorization:"Bearer ".concat(i.auth.getAccessToken()),"Content-Type":"application/octet-stream","Dropbox-API-Arg":k(t)}};return i.setCommonHeaders(s),s})).then((function(t){return i.fetch(g(s,i.domain,i.domainDelimiter)+e,t)})).then((function(e){return R(e)}))}},{key:"setCommonHeaders",value:function(e){this.selectUser&&(e.headers["Dropbox-API-Select-User"]=this.selectUser),this.selectAdmin&&(e.headers["Dropbox-API-Select-Admin"]=this.selectAdmin),this.pathRoot&&(e.headers["Dropbox-API-Path-Root"]=this.pathRoot)}}]),e}(),e.DropboxAuth=x,Object.defineProperty(e,"__esModule",{value:!0})}));
