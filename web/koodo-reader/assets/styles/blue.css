.delete-digest-button,
.add-dialog-comfirm,
.backup-page-backup-selector,
.delete-dialog-comfirm,
.book-item-config,
.book-cover-item-config,
.download-desk-button,
.edit-dialog-comfirm,
.change-location-button,
.token-dialog-comfirm,
.new-version-open,
.update-dialog-container-button,
.import-from-local,
.single-control-switch,
.side-menu-selector-container,
.previous-chapter-single-container,
.next-chapter-single-container,
.book-bookmark-link,
.message-box-container,
.only-local-icon {
  background-color: rgba(1, 121, 202, 1) !important;
}

.header-search-box,
.header-search-box::placeholder,
.header-search-text,
.card-list-item-show-more {
  color: rgba(1, 121, 202, 0.8) !important;
}
.single-control-container,
.book-list-view {
  color: rgba(1, 121, 202, 1) !important;
}
.header-search-box,
#jumpPage,
#jumpChapter,
#newTag {
  background-color: rgba(1, 121, 202, 0.1) !important;
}
.backup-page-close-icon:hover,
.sidebar-list-icon:hover,
.nav-close-icon:hover,
.setting-close-container:hover,
.side-menu-hover-container,
.setting-dialog-location-title,
.header-search-text:hover,
.reader-setting-icon-container:hover,
.setting-icon-container:hover,
.animation-mask,
.animation-mask-local,
.copy-option:hover {
  background-color: rgba(1, 121, 202, 0.035) !important;
}

.book-content-name,
.book-subcontent-name,
.book-bookmark-list,
.nav-search-list-item,
.sort-dialog-seperator {
  border-bottom: 1px solid rgba(1, 121, 202, 0.1) !important;
}
