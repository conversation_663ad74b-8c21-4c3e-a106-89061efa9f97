# AI工具栏轻量级实现 - 代码详解

## 1. 主类实现

### LightweightAIToolbar 类

**文件**: `electron/src/main/services/lightweight-ai-toolbar.ts`

```typescript
import { BrowserWindow, ipcMain } from 'electron';
import { AppWindow } from '../ui/windows';

export class LightweightAIToolbar {
  private parentWindow: AppWindow;
  private toolbarWindow: BrowserWindow | null = null;
  private currentTheme: string = 'light';
  private isVisible: boolean = true;
  private readonly TOOLBAR_WIDTH = 64;

  constructor(parentWindow: AppWindow) {
    this.parentWindow = parentWindow;
    this.setupIpcHandlers();
  }

  private setupIpcHandlers() {
    const { id } = this.parentWindow.win;

    // 处理工具按钮点击
    ipcMain.handle(`ai-toolbar-tool-click-${id}`, (e, toolId: string) => {
      return this.handleToolClick(toolId);
    });

    // 处理主题变化
    ipcMain.on(`ai-toolbar-theme-change-${id}`, (e, theme: string) => {
      this.updateTheme(theme);
    });

    // 获取当前主题
    ipcMain.handle(`ai-toolbar-get-theme-${id}`, () => {
      return this.currentTheme;
    });
  }

  public async create() {
    if (this.toolbarWindow) {
      return;
    }

    console.log('[LightweightAIToolbar] Creating lightweight toolbar window');

    const parentBounds = this.parentWindow.win.getBounds();

    this.toolbarWindow = new BrowserWindow({
      parent: this.parentWindow.win,
      width: this.TOOLBAR_WIDTH,
      height: parentBounds.height,
      x: parentBounds.x - this.TOOLBAR_WIDTH,
      y: parentBounds.y,
      frame: false,
      transparent: true,
      alwaysOnTop: false,
      skipTaskbar: true,
      resizable: false,
      minimizable: false,
      maximizable: false,
      closable: false,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        webSecurity: false,
        // 性能优化：禁用不必要的功能
        enableRemoteModule: false,
        allowRunningInsecureContent: false,
        experimentalFeatures: false,
        backgroundThrottling: false,
      },
    });

    // 加载HTML内容
    await this.toolbarWindow.loadURL(`data:text/html,${this.getToolbarHTML()}`);

    // 绑定事件
    this.bindEvents();

    console.log('[LightweightAIToolbar] Toolbar window created successfully');
  }

  private getToolbarHTML(): string {
    return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <style>
    ${this.getToolbarCSS()}
  </style>
</head>
<body data-theme="${this.currentTheme}">
  <div class="toolbar">
    <!-- Logo区域 - 为macOS窗口控制按钮预留空间 -->
    <div class="logo-area">
      <div class="logo">🤖</div>
    </div>

    <!-- 工具按钮区域 -->
    <div class="tools-area">
      <button class="tool-btn" data-tool="notes" title="智能笔记">📝</button>
      <button class="tool-btn" data-tool="memory" title="AI记忆">🧠</button>
      <button class="tool-btn" data-tool="clipboard" title="剪贴板">📋</button>
    </div>

    <!-- 设置按钮区域 -->
    <div class="settings-area">
      <button class="tool-btn" data-tool="settings" title="设置">⚙️</button>
    </div>
  </div>

  <script>
    ${this.getToolbarJS()}
  </script>
</body>
</html>
    `.replace(/\n\s+/g, '');
  }

  private getToolbarCSS(): string {
    return `
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        user-select: none;
      }

      :root {
        /* Light主题 */
        --bg-primary: #ffffff;
        --bg-secondary: #f5f5f5;
        --text-primary: #333333;
        --text-secondary: #666666;
        --border-color: #e0e0e0;
        --hover-bg: rgba(0,0,0,0.05);
        --active-bg: rgba(0,0,0,0.1);
      }

      [data-theme="dark"] {
        /* Dark主题 */
        --bg-primary: #2d2d2d;
        --bg-secondary: #3d3d3d;
        --text-primary: #ffffff;
        --text-secondary: #cccccc;
        --border-color: #555555;
        --hover-bg: rgba(255,255,255,0.1);
        --active-bg: rgba(255,255,255,0.15);
      }

      [data-theme="mario-dark"] {
        /* Mario自定义主题 */
        --bg-primary: #1a1a1a;
        --bg-secondary: #2a2a2a;
        --text-primary: #e0e0e0;
        --text-secondary: #b0b0b0;
        --border-color: #404040;
        --hover-bg: rgba(255,255,255,0.08);
        --active-bg: rgba(255,255,255,0.12);
      }

      body {
        width: 64px;
        height: 100vh;
        background: var(--bg-primary);
        border-right: 1px solid var(--border-color);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        overflow: hidden;
      }

      .toolbar {
        display: flex;
        flex-direction: column;
        height: 100%;
        width: 100%;
      }

      .logo-area {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 8px;
        margin-bottom: 16px;
      }

      .logo {
        font-size: 20px;
        opacity: 0.6;
      }

      .tools-area {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 0 8px;
      }

      .settings-area {
        padding: 8px;
        margin-bottom: 8px;
      }

      .tool-btn {
        width: 48px;
        height: 48px;
        background: var(--bg-secondary);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        cursor: pointer;
        font-size: 18px;
        color: var(--text-primary);
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .tool-btn:hover {
        background: var(--hover-bg);
        transform: translateY(-1px);
      }

      .tool-btn:active {
        background: var(--active-bg);
        transform: translateY(0);
      }

      .tool-btn:focus {
        outline: 2px solid var(--border-color);
        outline-offset: 2px;
      }
    `;
  }

  private getToolbarJS(): string {
    return `
      const { ipcRenderer } = require('electron');
      const windowId = ${this.parentWindow.win.id};

      // 处理工具按钮点击
      document.addEventListener('click', async (e) => {
        const toolBtn = e.target.closest('.tool-btn');
        if (toolBtn) {
          const toolId = toolBtn.getAttribute('data-tool');
          if (toolId) {
            try {
              await ipcRenderer.invoke(\`ai-toolbar-tool-click-\${windowId}\`, toolId);
            } catch (error) {
              console.error('Failed to handle tool click:', error);
            }
          }
        }
      });

      // 监听主题变化
      ipcRenderer.on('update-theme', (event, theme) => {
        document.body.setAttribute('data-theme', theme);
      });

      // 监听主题颜色变化
      ipcRenderer.on('update-theme-colors', (event, colors) => {
        const root = document.documentElement;
        if (colors.primary) root.style.setProperty('--bg-primary', colors.primary);
        if (colors.secondary) root.style.setProperty('--bg-secondary', colors.secondary);
        if (colors.text) root.style.setProperty('--text-primary', colors.text);
        if (colors.border) root.style.setProperty('--border-color', colors.border);
      });

      // 获取初始主题
      ipcRenderer.invoke(\`ai-toolbar-get-theme-\${windowId}\`).then(theme => {
        if (theme) {
          document.body.setAttribute('data-theme', theme);
        }
      });

      console.log('[AI Toolbar] Lightweight toolbar initialized');
    `;
  }

  private bindEvents() {
    if (!this.toolbarWindow) return;

    // 跟随主窗口移动
    this.parentWindow.win.on('move', () => {
      this.updatePosition();
    });

    // 跟随主窗口resize
    this.parentWindow.win.on('resize', () => {
      this.updatePosition();
    });

    // 主窗口最小化时隐藏工具栏
    this.parentWindow.win.on('minimize', () => {
      this.toolbarWindow?.hide();
    });

    // 主窗口恢复时显示工具栏
    this.parentWindow.win.on('restore', () => {
      if (this.isVisible) {
        this.toolbarWindow?.show();
      }
    });
  }

  public updatePosition() {
    if (!this.toolbarWindow || !this.isVisible) return;

    const parentBounds = this.parentWindow.win.getBounds();
    
    this.toolbarWindow.setBounds({
      x: parentBounds.x - this.TOOLBAR_WIDTH,
      y: parentBounds.y,
      width: this.TOOLBAR_WIDTH,
      height: parentBounds.height,
    });
  }

  public updateTheme(theme: string) {
    this.currentTheme = theme;
    if (this.toolbarWindow) {
      this.toolbarWindow.webContents.send('update-theme', theme);
    }
  }

  private async handleToolClick(toolId: string) {
    console.log('[LightweightAIToolbar] Tool clicked:', toolId);

    // 工具URL映射
    const toolActions = {
      notes: () => this.openNotesPage(),
      memory: () => this.openMemoryPage(),
      clipboard: () => this.openClipboardPage(),
      settings: () => this.openSettingsPage(),
    };

    const action = toolActions[toolId as keyof typeof toolActions];
    if (action) {
      await action();
    }
  }

  private async openNotesPage() {
    // 创建或切换到笔记页面
    this.parentWindow.viewManager.create({
      url: 'mario-ai://ai-notes',
      active: true,
    });
  }

  private async openMemoryPage() {
    // 创建或切换到记忆页面
    this.parentWindow.viewManager.create({
      url: 'mario-ai://ai-memory',
      active: true,
    });
  }

  private async openClipboardPage() {
    // 创建或切换到剪贴板页面
    this.parentWindow.viewManager.create({
      url: 'mario-ai://ai-clipboard',
      active: true,
    });
  }

  private async openSettingsPage() {
    // 创建或切换到设置页面
    this.parentWindow.viewManager.create({
      url: 'mario-ai://settings',
      active: true,
    });
  }

  public toggle(): boolean {
    this.isVisible = !this.isVisible;
    
    if (this.toolbarWindow) {
      if (this.isVisible) {
        this.toolbarWindow.show();
        this.updatePosition();
      } else {
        this.toolbarWindow.hide();
      }
    }

    return this.isVisible;
  }

  public destroy() {
    if (this.toolbarWindow) {
      this.toolbarWindow.close();
      this.toolbarWindow = null;
    }
  }

  public get width(): number {
    return this.isVisible ? this.TOOLBAR_WIDTH : 0;
  }

  public get visible(): boolean {
    return this.isVisible;
  }
}
```

## 2. 集成到AppWindow

**文件**: `electron/src/main/ui/windows/app.ts`

```typescript
// 导入
import { LightweightAIToolbar } from '@electron/main/services/lightweight-ai-toolbar';

export class AppWindow {
  // 添加属性
  public lightweightAIToolbar: LightweightAIToolbar;

  constructor(incognito: boolean) {
    // 现有构造函数代码...
    
    // 创建轻量级AI工具栏
    this.lightweightAIToolbar = new LightweightAIToolbar(this);
  }

  // 在ready-to-show事件中创建工具栏
  private setupWindowEvents() {
    this.win.on('ready-to-show', async () => {
      this.win.show();
      
      // 创建轻量级AI工具栏
      try {
        await this.lightweightAIToolbar.create();
        console.log('[AppWindow] Lightweight AI toolbar created');
      } catch (error) {
        console.error('[AppWindow] Failed to create AI toolbar:', error);
      }
    });

    // 其他事件处理...
  }
}
```

## 3. 主题同步集成

**文件**: `web/browser/src/core/utils/tailwind-theme-manager.ts`

```typescript
export class TailwindThemeManager {
  static setThemeWithAuto(theme: string, themeAuto: boolean) {
    // 现有主题设置逻辑...
    
    // 通知AI工具栏更新主题
    try {
      const { ipcRenderer } = require('electron');
      const windowId = (window as any).windowId || 1;
      ipcRenderer.send(`ai-toolbar-theme-change-${windowId}`, theme);
    } catch (error) {
      console.warn('Failed to notify AI toolbar theme change:', error);
    }
  }
}
```

## 4. ViewManager调整

**文件**: `electron/src/main/services/view-manager.ts`

```typescript
public async fixBounds() {
  // 现有代码...
  
  // 获取轻量级AI工具栏宽度
  const aiToolbarWidth = this.window.lightweightAIToolbar?.width || 0;

  const newBounds = {
    x: aiToolbarWidth,
    y: this.fullscreen ? 0 : toolbarContentHeight,
    width: width - aiToolbarWidth,
    height: this.fullscreen ? height : height - toolbarContentHeight,
  };

  // 设置BrowserView bounds...
}
```

这个实现提供了完整的轻量级AI工具栏解决方案，内存占用仅5-15MB，同时保持了通栏效果和主题同步功能。
