# Mario AI 开发指南

## 📋 概述

本文档为 Mario AI 浏览器集成项目的开发人员提供详细的开发指南，包括环境搭建、开发流程、代码规范和最佳实践。

## 🛠️ 开发环境搭建

### 系统要求
- **Node.js**: >= 18.0.0
- **Python**: >= 3.9.0
- **PNPM**: >= 8.0.0
- **操作系统**: Windows 10+, macOS 12+, Ubuntu 20.04+

### 环境安装

#### 1. 安装 Node.js 和 PNPM
```bash
# 安装 Node.js (推荐使用 nvm)
nvm install 18
nvm use 18

# 安装 PNPM
npm install -g pnpm@latest
```

#### 2. Python 服务准备
```bash
# Python服务为独立打包的外部服务
# 开发时需要确保Python服务可执行文件在指定路径
# 或配置正确的Python服务路径

# 检查Python服务是否可用
curl http://localhost:3002/health
```

#### 3. 克隆和初始化项目
```bash
# 克隆项目
git clone <repository-url>
cd mario-ai

# 安装依赖
pnpm install

# 初始化 Python 服务
cd services/python-server
poetry install
cd ../..
```

### 开发工具推荐

#### VS Code 扩展
- **TypeScript**: 官方 TypeScript 支持
- **Python**: 官方 Python 支持
- **ESLint**: 代码检查
- **Prettier**: 代码格式化
- **Thunder Client**: API 测试
- **GitLens**: Git 增强

#### 配置文件
```json
// .vscode/settings.json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "python.defaultInterpreterPath": "./services/python-server/.venv/bin/python"
}
```

## 🚀 开发流程

### 启动开发环境

#### 完整启动
```bash
# 启动所有服务
pnpm dev

# 这会同时启动：
# - Electron 应用
# - Web 前端 (热重载)
# - Node.js AI 服务
# - Python 服务
```

#### 单独启动服务
```bash
# 只启动 Electron 应用
pnpm dev:electron

# 只启动 Web 前端
pnpm dev:web

# 只启动 AI 服务
pnpm dev:ai-server

# 只启动 Python 服务
pnpm dev:python-server
```

### 调试配置

#### VS Code 调试配置
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Electron Main",
      "type": "node",
      "request": "launch",
      "cwd": "${workspaceFolder}/electron",
      "program": "${workspaceFolder}/electron/build/main.bundle.js",
      "env": {
        "NODE_ENV": "development"
      }
    },
    {
      "name": "Debug AI Server",
      "type": "node",
      "request": "launch",
      "cwd": "${workspaceFolder}/services/ai-server",
      "program": "${workspaceFolder}/services/ai-server/src/index.ts",
      "runtimeArgs": ["-r", "tsx/cjs"]
    },
    {
      "name": "Debug Python Server",
      "type": "python",
      "request": "launch",
      "cwd": "${workspaceFolder}/services/python-server",
      "program": "${workspaceFolder}/services/python-server/src/main.py"
    }
  ]
}
```

## 📝 代码规范

### TypeScript 规范

#### 命名约定
```typescript
// 组件名使用 PascalCase
export const ChatInterface: React.FC = () => {};

// 函数名使用 camelCase
export const formatMessage = (message: string) => {};

// 常量使用 UPPER_SNAKE_CASE
export const API_BASE_URL = 'http://localhost:3001';

// 类型名使用 PascalCase，接口以 I 开头
export interface IChatMessage {
  id: string;
  content: string;
}

export type ChatStatus = 'idle' | 'loading' | 'error';
```

#### 文件组织
```typescript
// 导入顺序：外部库 -> 内部模块 -> 相对路径
import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';

import { AIClient } from '@/services/ai-client';
import { ChatMessage } from '@/types/chat';

import './ChatInterface.css';
```

#### 组件规范
```typescript
// 组件接口定义
interface ChatInterfaceProps {
  sessionId?: string;
  onMessageSent?: (message: string) => void;
  className?: string;
}

// 组件实现
export const ChatInterface: React.FC<ChatInterfaceProps> = observer(({
  sessionId,
  onMessageSent,
  className
}) => {
  // Hooks
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  // Effects
  useEffect(() => {
    // 初始化逻辑
  }, [sessionId]);
  
  // Handlers
  const handleSendMessage = useCallback(async () => {
    // 发送消息逻辑
  }, [message, sessionId]);
  
  // Render
  return (
    <div className={cn('chat-interface', className)}>
      {/* 组件内容 */}
    </div>
  );
});

// 默认属性
ChatInterface.displayName = 'ChatInterface';
```

### Python 规范

#### 代码风格
```python
# 使用 Black 格式化，遵循 PEP 8
from typing import List, Optional, Dict, Any
import asyncio
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel

# 类名使用 PascalCase
class ChatMessage(BaseModel):
    id: str
    content: str
    role: str
    timestamp: Optional[str] = None

# 函数名使用 snake_case
async def process_chat_message(
    message: str,
    session_id: Optional[str] = None
) -> Dict[str, Any]:
    """处理聊天消息
    
    Args:
        message: 用户消息
        session_id: 会话ID
        
    Returns:
        处理结果字典
        
    Raises:
        HTTPException: 处理失败时抛出
    """
    try:
        # 处理逻辑
        result = await ai_service.generate_response(message)
        return {"success": True, "data": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# 常量使用 UPPER_SNAKE_CASE
API_VERSION = "v1"
MAX_MESSAGE_LENGTH = 4000
```

### CSS 规范

#### 样式组织
```css
/* 使用 BEM 命名约定 */
.chat-interface {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-interface__header {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.chat-interface__messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.chat-interface__input {
  padding: 1rem;
  border-top: 1px solid var(--border-color);
}

/* 状态修饰符 */
.chat-interface--loading {
  opacity: 0.7;
  pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-interface__header {
    padding: 0.5rem;
  }
}
```

## 🧪 测试指南

### 单元测试

#### React 组件测试
```typescript
// ChatInterface.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ChatInterface } from './ChatInterface';

describe('ChatInterface', () => {
  it('should render chat input', () => {
    render(<ChatInterface />);
    expect(screen.getByPlaceholderText('输入消息...')).toBeInTheDocument();
  });
  
  it('should send message when button clicked', async () => {
    const onMessageSent = jest.fn();
    render(<ChatInterface onMessageSent={onMessageSent} />);
    
    const input = screen.getByPlaceholderText('输入消息...');
    const button = screen.getByText('发送');
    
    fireEvent.change(input, { target: { value: 'Hello' } });
    fireEvent.click(button);
    
    await waitFor(() => {
      expect(onMessageSent).toHaveBeenCalledWith('Hello');
    });
  });
});
```

#### API 服务测试
```typescript
// chat-api.test.ts
import { AIClient } from './ai-client';

describe('AIClient', () => {
  let client: AIClient;
  
  beforeEach(() => {
    client = new AIClient('http://localhost:3001');
  });
  
  it('should send chat message', async () => {
    const response = await client.chat('Hello', 'session123');
    
    expect(response.success).toBe(true);
    expect(response.data.message).toBeDefined();
  });
});
```

#### Python 服务测试
```python
# test_chat_service.py
import pytest
from fastapi.testclient import TestClient
from src.main import app

client = TestClient(app)

def test_chat_message():
    response = client.post(
        "/api/chat/message",
        json={"message": "Hello", "sessionId": "test123"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "message" in data["data"]

@pytest.mark.asyncio
async def test_chat_service():
    from src.modules.chat.service import ChatService
    
    service = ChatService()
    result = await service.process_message("Hello")
    
    assert result is not None
    assert isinstance(result, str)
```

### 集成测试

#### E2E 测试
```typescript
// e2e/chat.spec.ts
import { test, expect } from '@playwright/test';

test('chat functionality', async ({ page }) => {
  await page.goto('http://localhost:3000');
  
  // 点击聊天工具按钮
  await page.click('[data-testid="chat-tool-button"]');
  
  // 等待聊天界面加载
  await expect(page.locator('.chat-interface')).toBeVisible();
  
  // 发送消息
  await page.fill('[data-testid="chat-input"]', 'Hello AI');
  await page.click('[data-testid="send-button"]');
  
  // 验证消息发送
  await expect(page.locator('.message--user')).toContainText('Hello AI');
  
  // 等待AI回复
  await expect(page.locator('.message--assistant')).toBeVisible();
});
```

## 🔧 添加新功能

### 添加新 AI 工具的步骤

#### 1. 创建前端模块
```bash
# 创建模块目录
mkdir -p web/browser/src/ai-modules/new-tool/{components,hooks,services,types}

# 创建基础文件
touch web/browser/src/ai-modules/new-tool/components/NewToolManager.tsx
touch web/browser/src/ai-modules/new-tool/hooks/useNewTool.ts
touch web/browser/src/ai-modules/new-tool/services/new-tool-api.ts
touch web/browser/src/ai-modules/new-tool/types/new-tool.types.ts
```

#### 2. 创建后端服务
```bash
# 创建服务模块
mkdir -p services/ai-server/src/modules/new-tool

# 创建服务文件
touch services/ai-server/src/modules/new-tool/routes.ts
touch services/ai-server/src/modules/new-tool/controller.ts
touch services/ai-server/src/modules/new-tool/service.ts
```

#### 3. 更新配置
```typescript
// 在 AIToolbar 中添加按钮
const tools = [
  // 现有工具...
  { id: 'new-tool', icon: '🔧', label: '新工具' }
];

// 在 AITabs 中添加标签页组件
{toolType === 'new-tool' && <NewToolManager />}

// 在类型定义中添加新类型
export type AIToolType = 'chat' | 'notes' | 'memory' | 'clipboard' | 'new-tool';
```

#### 4. 数据库迁移
```sql
-- 添加新工具相关表
CREATE TABLE new_tool_data (
  id TEXT PRIMARY KEY,
  -- 其他字段...
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 最佳实践

#### 1. 错误处理
```typescript
// 统一错误处理
export const handleAPIError = (error: any) => {
  console.error('API Error:', error);
  
  if (error.response?.status === 401) {
    // 处理认证错误
    return '认证失败，请重新登录';
  } else if (error.response?.status >= 500) {
    // 处理服务器错误
    return '服务器错误，请稍后重试';
  } else {
    // 处理其他错误
    return error.message || '未知错误';
  }
};
```

#### 2. 性能优化
```typescript
// 使用 React.memo 优化组件
export const ChatMessage = React.memo<ChatMessageProps>(({ message }) => {
  return <div>{message.content}</div>;
});

// 使用 useMemo 优化计算
const filteredMessages = useMemo(() => {
  return messages.filter(msg => msg.content.includes(searchTerm));
}, [messages, searchTerm]);

// 使用 useCallback 优化函数
const handleSendMessage = useCallback(async (content: string) => {
  await aiClient.sendMessage(content);
}, [aiClient]);
```

#### 3. 类型安全
```typescript
// 严格的类型定义
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
  };
  timestamp: string;
}

// 使用泛型确保类型安全
export const apiCall = async <T>(
  endpoint: string,
  options?: RequestInit
): Promise<APIResponse<T>> => {
  const response = await fetch(endpoint, options);
  return response.json();
};
```

## 📚 常用命令

### 开发命令
```bash
# 启动开发环境
pnpm dev

# 构建项目
pnpm build

# 运行测试
pnpm test

# 代码检查
pnpm lint

# 代码格式化
pnpm format

# 类型检查
pnpm type-check
```

### 服务管理
```bash
# 重启 AI 服务
pnpm restart:ai-server

# 查看服务状态
pnpm status:services

# 清理缓存
pnpm clean:cache

# 重新安装依赖
pnpm clean:deps && pnpm install
```

### 数据库操作
```bash
# 运行数据库迁移
pnpm db:migrate

# 生成迁移文件
pnpm db:generate

# 查看数据库状态
pnpm db:status

# 重置数据库
pnpm db:reset
```

## 🐛 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查端口占用
lsof -i :3001
lsof -i :3002

# 清理进程
pkill -f "ai-server"
pkill -f "python-server"

# 重新启动
pnpm dev:services
```

#### 2. 依赖安装问题
```bash
# 清理 node_modules
rm -rf node_modules
rm -rf */node_modules
rm pnpm-lock.yaml

# 重新安装
pnpm install
```

#### 3. Python 环境问题
```bash
# 重建 Python 环境
cd services/python-server
poetry env remove python
poetry install
```

### 调试技巧

#### 1. 网络请求调试
```typescript
// 添加请求拦截器
const originalFetch = window.fetch;
window.fetch = async (...args) => {
  console.log('Fetch request:', args);
  const response = await originalFetch(...args);
  console.log('Fetch response:', response);
  return response;
};
```

#### 2. 状态调试
```typescript
// 使用 MobX 开发工具
import { configure } from 'mobx';

if (process.env.NODE_ENV === 'development') {
  configure({
    enforceActions: 'always',
    computedRequiresReaction: true,
    reactionRequiresObservable: true,
    observableRequiresReaction: true,
    disableErrorBoundaries: true
  });
}
```

---

**文档版本**: v1.0  
**创建日期**: 2024-01-31  
**最后更新**: 2024-01-31  
**维护者**: Mario AI Team
