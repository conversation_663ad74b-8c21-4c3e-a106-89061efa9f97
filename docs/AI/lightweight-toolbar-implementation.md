# AI工具栏轻量级实现方案（方案F）

## 概述

本文档详细描述了AI工具栏的轻量级实现方案，通过使用Electron子窗口替代BrowserView，实现通栏效果的同时大幅降低内存占用。

## 方案对比

| 特性 | 当前方案（BrowserView） | 方案F（轻量子窗口） |
|------|------------------------|-------------------|
| 内存占用 | 80-90MB | 5-15MB |
| 启动时间 | 200-500ms | 50-100ms |
| 通栏效果 | ✅ | ✅ |
| 主题同步 | 自动 | 需要IPC同步 |
| 开发复杂度 | 高（多BrowserView） | 中（子窗口+IPC） |
| 技术栈 | React + BrowserView | 原生HTML/CSS/JS |

## 架构设计

### 进程结构
```
Electron应用
├── 主进程 (Electron Main)
├── 主窗口 webContents (浏览器UI + 页面内容)
└── AI工具栏子窗口 (轻量级HTML) ← 新增
```

### 窗口布局
```
┌─────────────────────────────────────┐
│ 屏幕区域                             │
├──────┬──────────────────────────────┤
│AI工具│ 主浏览器窗口                  │
│栏子窗│ ├─ Titlebar                  │
│口64px│ ├─ Toolbar                   │
│      │ ├─ BookmarkBar               │
│      │ └─ 页面内容 (BrowserView)     │
└──────┴──────────────────────────────┘
```

## 核心组件

### 1. LightweightAIToolbar 类

**文件位置**: `electron/src/main/services/lightweight-ai-toolbar.ts`

**职责**:
- 创建和管理AI工具栏子窗口
- 处理主题同步
- 管理窗口位置和大小
- 处理工具按钮点击事件

### 2. 极简HTML模板

**特点**:
- 纯HTML/CSS/JS实现
- 支持CSS变量主题系统
- 直接IPC通信
- 最小化依赖

### 3. 主题同步系统

**机制**:
- IPC事件驱动的主题同步
- CSS变量动态更新
- 支持多种主题模式

## 详细实现

### 1. 主类实现

```javascript
class LightweightAIToolbar {
  constructor(parentWindow) {
    this.parentWindow = parentWindow;
    this.toolbarWindow = null;
    this.currentTheme = 'light';
    this.isVisible = true;
    
    this.setupIpcHandlers();
  }

  async create() {
    // 创建子窗口
    // 加载HTML模板
    // 设置初始位置
    // 绑定事件监听
  }

  updatePosition() {
    // 根据主窗口位置调整工具栏位置
  }

  updateTheme(theme) {
    // 同步主题到工具栏窗口
  }

  destroy() {
    // 清理资源
  }
}
```

### 2. HTML模板结构

```html
<!DOCTYPE html>
<html>
<head>
  <style>
    /* CSS变量主题系统 */
    /* 响应式布局 */
    /* 按钮样式 */
  </style>
</head>
<body data-theme="light">
  <div class="toolbar">
    <div class="logo-area"></div>
    <div class="tools-area">
      <!-- 工具按钮 -->
    </div>
    <div class="settings-area">
      <!-- 设置按钮 -->
    </div>
  </div>
  
  <script>
    /* IPC通信逻辑 */
    /* 主题更新处理 */
    /* 事件处理 */
  </script>
</body>
</html>
```

## 集成步骤

### 步骤1: 创建轻量级工具栏类
1. 在 `electron/src/main/services/` 创建 `lightweight-ai-toolbar.ts`
2. 实现基础的窗口创建和管理逻辑
3. 添加IPC事件处理

### 步骤2: 设计HTML模板
1. 创建极简的HTML结构
2. 实现CSS变量主题系统
3. 添加JavaScript事件处理

### 步骤3: 集成到主窗口
1. 在AppWindow中集成LightweightAIToolbar
2. 处理窗口位置同步
3. 实现主题变化通知

### 步骤4: 调整主窗口布局
1. 修改主窗口的初始位置和大小
2. 调整ViewManager的bounds计算
3. 确保页面内容正确显示

### 步骤5: 测试和优化
1. 测试不同主题的切换
2. 验证窗口resize行为
3. 检查内存占用和性能

## 主题同步实现

### CSS变量系统
```css
:root {
  --toolbar-bg: #ffffff;
  --toolbar-text: #333333;
  --button-hover: rgba(0,0,0,0.05);
}

[data-theme="dark"] {
  --toolbar-bg: #2d2d2d;
  --toolbar-text: #ffffff;
  --button-hover: rgba(255,255,255,0.1);
}
```

### IPC通信流程
```
主应用主题变化 → IPC发送 → 工具栏接收 → 更新CSS变量 → 视觉更新
```

## 性能优化

### 内存优化
- 禁用不必要的Chromium功能
- 最小化HTML/CSS/JS代码
- 避免复杂的DOM结构

### 启动优化
- 预编译HTML模板
- 延迟加载非关键功能
- 优化IPC通信频率

## 风险评估

### 技术风险
- **中等**: 子窗口位置同步复杂性
- **低**: 主题同步延迟
- **低**: 跨平台兼容性

### 维护风险
- **低**: 代码量少，逻辑简单
- **中等**: 需要手动维护主题映射
- **低**: 依赖少，稳定性好

## 后续扩展

### 可扩展功能
- 工具栏自定义配置
- 更多主题支持
- 动画效果增强
- 快捷键支持

### 性能监控
- 内存使用监控
- 启动时间统计
- IPC通信延迟测量

## 总结

方案F通过使用轻量级子窗口替代重量级BrowserView，在保持通栏效果的同时显著降低了内存占用和启动时间。虽然需要手动处理主题同步，但整体实现复杂度可控，是一个平衡性能和功能需求的优秀方案。
