# AI工具栏迁移指南 - 从BrowserView到轻量级子窗口

## 迁移概述

本指南详细说明如何从当前的BrowserView实现迁移到轻量级子窗口实现，以实现更好的性能和更低的内存占用。

## 迁移前后对比

### 当前实现（BrowserView）
```
内存占用: 80-90MB
启动时间: 200-500ms
技术栈: React + BrowserView + IPC
文件数量: 8个文件
代码行数: ~500行
```

### 目标实现（轻量级子窗口）
```
内存占用: 5-15MB
启动时间: 50-100ms
技术栈: 原生HTML/CSS/JS + 子窗口
文件数量: 2个文件
代码行数: ~300行
```

## 迁移步骤

### 第一阶段：准备工作

#### 1. 备份当前实现
```bash
# 创建备份分支
git checkout -b backup/ai-toolbar-browserview

# 备份相关文件
mkdir -p backup/ai-toolbar-old
cp -r web/browser/src/views/ai-toolbar backup/ai-toolbar-old/
cp electron/src/main/services/ai-toolbar-manager.ts backup/ai-toolbar-old/
```

#### 2. 创建新的实现文件
```bash
# 创建轻量级实现
touch electron/src/main/services/lightweight-ai-toolbar.ts
```

### 第二阶段：实现轻量级工具栏

#### 1. 创建LightweightAIToolbar类
- 复制 `docs/AI/lightweight-toolbar-code.md` 中的完整实现
- 放置到 `electron/src/main/services/lightweight-ai-toolbar.ts`

#### 2. 更新AppWindow集成
```typescript
// 在 electron/src/main/ui/windows/app.ts 中

// 替换导入
- import {AIToolbarManager} from '@electron/main/services/ai-toolbar-manager';
+ import {LightweightAIToolbar} from '@electron/main/services/lightweight-ai-toolbar';

// 替换属性
- public aiToolbarManager: AIToolbarManager;
+ public lightweightAIToolbar: LightweightAIToolbar;

// 替换构造函数中的初始化
- this.aiToolbarManager = new AIToolbarManager(this);
+ this.lightweightAIToolbar = new LightweightAIToolbar(this);

// 更新ready-to-show事件处理
- await this.aiToolbarManager.create();
+ await this.lightweightAIToolbar.create();
```

#### 3. 更新ViewManager
```typescript
// 在 electron/src/main/services/view-manager.ts 中

// 更新fixBounds方法
- const aiToolbarWidth = this.window.aiToolbarManager?.width || 0;
+ const aiToolbarWidth = this.window.lightweightAIToolbar?.width || 0;
```

#### 4. 更新窗口事件处理
```typescript
// 在 electron/src/main/ui/windows/app.ts 中

// 更新resize函数
const resize = () => {
  setTimeout(() => {
-   this.aiToolbarManager?.updateBounds();
+   this.lightweightAIToolbar?.updatePosition();
    
    if (process.platform === 'linux') {
      this.viewManager.select(this.viewManager.selectedId, false, true);
    } else {
      this.viewManager.fixBounds();
    }
  });
  // ...
};

// 更新全屏事件
this.win.on('enter-full-screen', () => {
  this.send('fullscreen', true);
- this.aiToolbarManager?.updateBounds();
+ this.lightweightAIToolbar?.updatePosition();
  this.viewManager.fixBounds();
});

this.win.on('leave-full-screen', () => {
  this.send('fullscreen', false);
- this.aiToolbarManager?.updateBounds();
+ this.lightweightAIToolbar?.updatePosition();
  this.viewManager.fixBounds();
});
```

### 第三阶段：清理旧实现

#### 1. 移除BrowserView相关文件
```bash
# 移除旧的AI工具栏实现
rm -rf web/browser/src/views/ai-toolbar
rm electron/src/main/services/ai-toolbar-manager.ts
```

#### 2. 更新Vite配置
```typescript
// 在 web/browser/vite.config.ts 中移除
- 'ai-toolbar': resolve(__dirname, 'pages/ai-toolbar.html'),
```

#### 3. 移除HTML页面
```bash
rm web/browser/pages/ai-toolbar.html
```

#### 4. 清理App Store
```typescript
// 在 web/browser/src/views/app/store/index.ts 中移除
- public aiToolbarVisible = true;
- aiToolbarVisible: observable,
- // 监听AI工具栏可见性变化相关代码
```

#### 5. 清理App组件
```typescript
// 在 web/browser/src/views/app/components/App/index.tsx 中移除
- // 为AI工具栏预留空间相关代码
- const aiToolbarWidth = store.aiToolbarVisible && !store.isFullscreen ? 64 : 0;
- marginLeft: `${aiToolbarWidth}px`,
```

### 第四阶段：主题同步集成

#### 1. 更新主题管理器
```typescript
// 在 web/browser/src/core/utils/tailwind-theme-manager.ts 中添加
export class TailwindThemeManager {
  static setThemeWithAuto(theme: string, themeAuto: boolean) {
    // 现有代码...
    
    // 通知轻量级AI工具栏更新主题
    try {
      const { ipcRenderer } = require('electron');
      const windowId = (window as any).windowId || 1;
      ipcRenderer.send(`ai-toolbar-theme-change-${windowId}`, theme);
    } catch (error) {
      console.warn('Failed to notify AI toolbar theme change:', error);
    }
  }
}
```

### 第五阶段：测试和验证

#### 1. 功能测试清单
- [ ] AI工具栏正确显示（通栏效果）
- [ ] 工具按钮点击正常工作
- [ ] 主题切换正确同步
- [ ] 窗口resize时位置正确
- [ ] 窗口移动时跟随正确
- [ ] 全屏模式下行为正确
- [ ] 最小化/恢复行为正确

#### 2. 性能测试
```bash
# 检查内存占用
# 在任务管理器或Activity Monitor中查看进程内存

# 检查启动时间
# 在控制台中查看启动日志时间戳
```

#### 3. 回滚计划
如果迁移出现问题，可以快速回滚：
```bash
# 回滚到备份分支
git checkout backup/ai-toolbar-browserview

# 或者恢复备份文件
cp -r backup/ai-toolbar-old/* ./
```

## 迁移后的优势

### 性能提升
- **内存节省**: 75-85MB (从90MB降到5-15MB)
- **启动加速**: 150-450ms (从500ms降到50ms)
- **CPU使用降低**: 减少进程间通信开销

### 开发体验改善
- **代码简化**: 从500行降到300行
- **文件减少**: 从8个文件降到2个文件
- **调试简化**: 减少多进程调试复杂性

### 维护成本降低
- **依赖减少**: 移除React依赖
- **构建简化**: 无需额外的HTML页面构建
- **测试简化**: 减少跨进程测试场景

## 注意事项

### 兼容性
- 确保Electron版本支持子窗口特性
- 测试不同操作系统的表现

### 主题同步
- 主题变化可能有微小延迟（<10ms）
- 需要手动维护主题变量映射

### 窗口管理
- 子窗口的生命周期与主窗口绑定
- 需要正确处理窗口事件

## 故障排除

### 常见问题

1. **工具栏不显示**
   - 检查子窗口是否正确创建
   - 验证窗口位置计算是否正确

2. **主题不同步**
   - 检查IPC事件是否正确发送
   - 验证CSS变量是否正确更新

3. **位置不正确**
   - 检查窗口bounds计算逻辑
   - 验证事件监听是否正确绑定

4. **性能没有改善**
   - 检查是否完全移除了旧的BrowserView
   - 验证子窗口配置是否优化

通过遵循这个迁移指南，可以安全地从BrowserView实现迁移到轻量级子窗口实现，获得显著的性能提升和更好的用户体验。
