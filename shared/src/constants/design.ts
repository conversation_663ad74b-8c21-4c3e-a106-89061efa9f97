export const DEFAULT_TAB_MARGIN_TOP = 8;
export const COMPACT_TAB_MARGIN_TOP = 8;

export const DEFAULT_TAB_HEIGHT = 28;
export const COMPACT_TAB_HEIGHT = 28;

// Toolbar
export const TOOLBAR_HEIGHT = 42;

export const TOOLBAR_BUTTON_WIDTH = 36;
export const TOOLBAR_BUTTON_HEIGHT = 32;

export const ADD_TAB_BUTTON_WIDTH = 28;
export const ADD_TAB_BUTTON_HEIGHT = 28;

export const DEFAULT_TITLEBAR_HEIGHT =
  DEFAULT_TAB_MARGIN_TOP * 2 + DEFAULT_TAB_HEIGHT;
export const COMPACT_TITLEBAR_HEIGHT =
  2 * COMPACT_TAB_MARGIN_TOP + COMPACT_TAB_HEIGHT;

export const VIEW_Y_OFFSET = TOOLBAR_HEIGHT + DEFAULT_TITLEBAR_HEIGHT;

// Widths
export const WINDOWS_BUTTON_WIDTH = 45;
export const MENU_WIDTH = 330;

// Dialogs
export const DIALOG_MIN_HEIGHT = 130;
export const DIALOG_MARGIN = 16;
export const DIALOG_TOP = 34;
export const DIALOG_MARGIN_TOP = 3;


export const EASING_FUNCTION = 'cubic-bezier(0.4, 0, 0.2, 1)';

export const DIALOG_EASING = 'cubic-bezier(0.1, 0.9, 0.2, 1)';

export const ERROR_COLOR = '#b00020';
