{"name": "@mario-ai/shared", "version": "1.1.2", "description": "Mario AI Browser - Shared Code", "main": "dist/index.js", "module": "dist/index.js", "type": "module", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist"}, "devDependencies": {"typescript": "^4.3.4", "rimraf": "^3.0.2", "@types/node": "^15.12.5"}}